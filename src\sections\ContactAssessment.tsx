import { ASSESSMENT_TYPES, AssessmentType } from "#pages/data";
import {
  Badge,
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Grid,
  Heading,
  HStack,
  Icon,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Select,
  SimpleGrid,
  Stack,
  Text,
  Textarea,
  useColorModeValue,
  useDisclosure,
  VStack,
} from "@chakra-ui/react";
import React, { useState } from "react";
import {
  FiArrowRight,
  FiCalendar,
  FiMail,
  FiPhone,
} from "react-icons/fi/index.js";

export const ContactAssessment: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedAssessment, setSelectedAssessment] =
    useState<AssessmentType | null>(null);

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const headerColor = useColorModeValue("gray.600", "gray.400");
  return (
    <Box
      as="section"
      py={{ base: 16, md: 24 }}
      bg={useColorModeValue("gray.50", "gray.900")}
    >
      <Container maxW="container.xl">
        <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={12}>
          {/* Quick Contact Side */}
          <Stack spacing={8}>
            <Stack spacing={4}>
              <Heading size="2xl">
                Let's Start Your Digital Transformation
              </Heading>
              <Text
                fontSize="xl"
                color={useColorModeValue("gray.600", "gray.400")}
              >
                Schedule a consultation or get an instant assessment of your IT
                needs
              </Text>
            </Stack>

            <Stack spacing={6}>
              <HStack spacing={4}>
                <Icon as={FiPhone} boxSize={6} color="blue.500" />
                <VStack align="start" spacing={1}>
                  <Text fontWeight="bold">Call Us</Text>
                  <Text>(*************</Text>
                </VStack>
              </HStack>

              <HStack spacing={4}>
                <Icon as={FiMail} boxSize={6} color="blue.500" />
                <VStack align="start" spacing={1}>
                  <Text fontWeight="bold">Email Us</Text>
                  <Text><EMAIL></Text>
                </VStack>
              </HStack>

              <HStack spacing={4}>
                <Icon as={FiCalendar} boxSize={6} color="blue.500" />
                <VStack align="start" spacing={1}>
                  <Text fontWeight="bold">Schedule a Call</Text>
                  <Button
                    variant="link"
                    colorScheme="blue"
                    rightIcon={<Icon as={FiArrowRight} />}
                    onClick={() => window.open("your-calendly-link")}
                  >
                    Book a time that works for you
                  </Button>
                </VStack>
              </HStack>
            </Stack>
          </Stack>

          {/* Assessment Options Side */}
          <Stack spacing={8}>
            <Heading size="lg">Get an Instant Assessment</Heading>

            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              {ASSESSMENT_TYPES.map((assessment) => (
                <Box
                  key={assessment.id}
                  p={6}
                  bg={bgColor}
                  borderRadius="xl"
                  borderWidth="1px"
                  borderColor={borderColor}
                  cursor="pointer"
                  transition="all 0.3s"
                  _hover={{
                    transform: "translateY(-4px)",
                    shadow: "lg",
                  }}
                  onClick={() => {
                    setSelectedAssessment(assessment);
                    onOpen();
                  }}
                >
                  <Stack spacing={4}>
                    <Icon as={assessment.icon} boxSize={8} color="blue.500" />
                    <Stack spacing={2}>
                      <Heading size="md">{assessment.title}</Heading>
                      <Text fontSize="sm" color={headerColor}>
                        {assessment.description}
                      </Text>
                    </Stack>
                    <HStack>
                      <Badge colorScheme="blue">
                        {assessment.estimatedTime}
                      </Badge>
                      <Badge variant="outline">
                        {assessment.questions.length} questions
                      </Badge>
                    </HStack>
                  </Stack>
                </Box>
              ))}
            </SimpleGrid>
          </Stack>
        </Grid>
      </Container>

      {/* Assessment Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{selectedAssessment?.title}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Stack spacing={6} pb={6}>
              <form onSubmit={(e) => e.preventDefault()}>
                <Stack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel>Name</FormLabel>
                    <Input placeholder="Your name" />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Email</FormLabel>
                    <Input type="email" placeholder="Your email" />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Company Size</FormLabel>
                    <Select>
                      <option value="1-10">1-10 employees</option>
                      <option value="11-50">11-50 employees</option>
                      <option value="51-200">51-200 employees</option>
                      <option value="201+">201+ employees</option>
                    </Select>
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Primary Challenge</FormLabel>
                    <Textarea placeholder="Brief description of your main challenge" />
                  </FormControl>

                  <Button
                    colorScheme="blue"
                    size="lg"
                    rightIcon={<Icon as={FiArrowRight} />}
                    type="submit"
                  >
                    Start Assessment
                  </Button>
                </Stack>
              </form>
            </Stack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};
