// src/components/HomePage.jsx
import {
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Select,
  Text,
  VStack,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import React, { useState } from "react";
import { z } from "zod";

const schema = z.object({
  contactName: z.string().min(2, "Contact name is required"),
  company: z.string().min(2, "Company is required"),
  product: z.enum(["DIA", "Private Line", "MPLS", "SIP Trunk"], {
    required_error: "Product is required",
  }),
  phone: z
    .string()
    .regex(/^\d{7,}$/, "Phone must be numeric and at least 7 digits"),
  email: z.string().email("Valid email required"),
  locations: z.string().min(3, "Service address is required"),
});
type Schema = z.infer<typeof schema>;

const initialForm: Schema = {
  contactName: "",
  company: "",
  product: "DIA",
  phone: "",
  email: "",
  locations: "",
};

const LeadGen = () => {
  const [formData, setFormData] = useState<Schema>(initialForm);
  const [errors, setErrors] = useState<Partial<Record<keyof Schema, string>>>(
    {}
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const toast = useToast();

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: undefined }));
  };

  // --- Zoho CRM API integration ---
  async function sendToZohoCRM(
    formData: Schema
  ): Promise<{ success: boolean; message: string }> {
    const response = await fetch("/.netlify/functions/submit-to-zoho", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      // We only send the non-sensitive form data.
      body: JSON.stringify(formData),
    });

    const data = await response.json();
    console.log(data);
    if (response.ok && data.data && data.data[0].code === "SUCCESS") {
      return { success: true, message: "Lead sent to Zoho CRM." };
    } else {
      return {
        success: false,
        message: data.data?.[0]?.message || "Zoho CRM error.",
      };
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      schema.parse(formData);
      setIsSubmitting(true);
      const result = await sendToZohoCRM(formData);

      setIsSubmitting(false);
      if (result.success) {
        setFormData(initialForm);
        toast({
          title: "Quote request sent!",
          description: "We'll be in touch soon.",
          status: "success",
          duration: 4000,
          isClosable: true,
        });
      } else {
        toast({
          title: "Submission failed",
          description: result.message,
          status: "error",
          duration: 6000,
          isClosable: true,
        });
      }
    } catch (err: any) {
      setIsSubmitting(false);
      if (err.errors) {
        const fieldErrors: Partial<Record<keyof Schema, string>> = {};
        err.errors.forEach((e: any) => {
          fieldErrors[e.path[0] as keyof Schema] = e.message;
        });
        setErrors(fieldErrors);
      }
    }
  };

  // Card styling for hero section
  const cardShadow = useColorModeValue("white-2xl", "dark-lg");
  const textColor = useColorModeValue("gray.900", "gray.200");

  return (
    <Box
      boxShadow={cardShadow}
      borderRadius="2xl"
      p={{ base: 4, md: 6 }}
      w="full"
      maxW="md"
      mx="auto"
      color={textColor}
    >
      <Heading as="h2" size="lg" mb={2} textAlign="center">
        Request a Free Quote
      </Heading>
      <Text
        // as="p"
        fontSize={{ base: "sm", md: "md" }}
        // color="gray.800"
        mb={3}
        textAlign="center"
      >
        Get an instant quote for business connectivity. Use our quick form
        below—no waiting, no hassle. Fast pricing for Cable, Fiber, DIA, Private
        Line, MPLS, or SIP Trunk services.
      </Text>
      <Box
        as="form"
        id="lead_gen_form"
        onSubmit={handleSubmit}
        name="lead_gen_form"
        // method="POST"
      >
        <VStack spacing={4} align="stretch">
          <FormControl isInvalid={!!errors.contactName} isRequired>
            <FormLabel>Contact Name</FormLabel>
            <Input
              name="contactName"
              variant="outline"
              value={formData.contactName}
              onChange={handleChange}
              placeholder="Your Name"
              autoComplete="name"
            />
            <FormErrorMessage>{errors.contactName}</FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.company} isRequired>
            <FormLabel>Company</FormLabel>
            <Input
              name="company"
              value={formData.company}
              onChange={handleChange}
              placeholder="Company Name"
              autoComplete="organization"
            />
            <FormErrorMessage>{errors.company}</FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.product} isRequired>
            <FormLabel>Select Product</FormLabel>
            <Select
              name="product"
              value={formData.product}
              onChange={handleChange}
            >
              <option value="Cable">Cable</option>
              <option value="Fiber">Fiber</option>
              <option value="DIA">DIA</option>
              <option value="Private Line">Private Line</option>
              <option value="MPLS">MPLS</option>
              <option value="SIP Trunk">SIP Trunk</option>
            </Select>
            <FormErrorMessage>{errors.product}</FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.phone} isRequired>
            <FormLabel>Contact Phone Number</FormLabel>
            <Input
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Phone Number"
              autoComplete="tel"
              inputMode="numeric"
            />
            <FormErrorMessage>{errors.phone}</FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.email} isRequired>
            <FormLabel>Contact Email</FormLabel>
            <Input
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Email Address"
              autoComplete="email"
              type="email"
            />
            <FormErrorMessage>{errors.email}</FormErrorMessage>
          </FormControl>
          <FormControl isInvalid={!!errors.locations} isRequired>
            <FormLabel>Locations (Service Address)</FormLabel>
            <Input
              name="locations"
              value={formData.locations}
              onChange={handleChange}
              placeholder="Service Address(es)"
              autoComplete="street-address"
            />
            <FormErrorMessage>{errors.locations}</FormErrorMessage>
          </FormControl>
          <Button
            type="submit"
            variant="solid"
            size="lg"
            isLoading={isSubmitting}
            loadingText="Sending..."
            mt={2}
            w="full"
          >
            Request Quote
          </Button>
        </VStack>
      </Box>
    </Box>
  );
};

export default LeadGen;
