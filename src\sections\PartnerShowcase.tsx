import { Partner, PARTNERS } from "#pages/data";
import {
  Box,
  Container,
  Flex,
  Heading,
  Image,
  ScaleFade,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react";
import React from "react";
import { useInView } from "react-intersection-observer";

const PartnerCard: React.FC<{ partner: Partner }> = ({ partner }) => {
  const { isOpen, onOpen } = useDisclosure();
  const { ref, inView } = useInView({
    threshold: 0.2,
    triggerOnce: true,
  });

  React.useEffect(() => {
    if (inView) onOpen();
  }, [inView, onOpen]);

  return (
    <ScaleFade in={isOpen} initialScale={0.9}>
      <Box
        ref={ref}
        bg={useColorModeValue("white", "gray.800")}
        p={6}
        borderRadius="xl"
        boxShadow="lg"
        transition="all 0.3s"
        _hover={{
          transform: "translateY(-4px)",
          boxShadow: "xl",
        }}
      >
        <Stack spacing={0}>
          <Flex h="100px" align="center" justify="center" p={4}>
            <Image
              src={partner.logo}
              alt={partner.name}
              maxH="80px"
              objectFit="contain"
              filter={useColorModeValue("none", "brightness(0.8)")}
            />
          </Flex>

          <Text fontWeight="bold" fontSize="lg" align={"center"}>
            {partner.name}
          </Text>
        </Stack>
      </Box>
    </ScaleFade>
  );
};

export const PartnerShowcase: React.FC = () => {
  return (
    <Box
      as="section"
      py={{ base: 16, md: 24 }}
      bg={useColorModeValue("gray.50", "gray.900")}
    >
      <Container maxW="container.xl">
        <Stack spacing={12}>
          <Stack spacing={4} textAlign="center">
            <Heading size="2xl" fontWeight="bold">
              Our Technology Partners
            </Heading>
            <Text
              fontSize="xl"
              color={useColorModeValue("gray.600", "gray.400")}
              maxW="3xl"
              mx="auto"
            >
              Leveraging partnerships with industry leaders to deliver
              enterprise-grade solutions
            </Text>
          </Stack>

          <SimpleGrid
            columns={{ base: 1, md: 2, lg: 4 }}
            spacing={8}
            px={{ base: 4, md: 8 }}
          >
            {PARTNERS.map((partner, index) => (
              <PartnerCard key={index} partner={partner as any} />
            ))}
          </SimpleGrid>
        </Stack>
      </Container>
    </Box>
  );
};
