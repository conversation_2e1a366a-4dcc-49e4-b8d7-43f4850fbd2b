import { Box } from "@chakra-ui/react";
import { motion } from "framer-motion";
import React, { ReactNode } from "react";

const MotionBox = motion(Box);

const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  enter: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeInOut",
      when: "beforeChildren",
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.3,
      ease: "easeInOut",
      when: "afterChildren",
    },
  },
};

export const PageTransition = ({ children }: { children: ReactNode }) => {
  return (
    <MotionBox
      initial="initial"
      animate="enter"
      exit="exit"
      variants={pageVariants}
    >
      {children}
    </MotionBox>
  );
};
