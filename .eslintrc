{
  "env": { "browser": true, "es2020": true },
  "extends": [
    "react-app",
    "eslint:recommended",
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["react-refresh"],
  "rules": {
    "react-refresh/only-export-components": "warn",
    "no-unused-vars": 0
  },
  "settings": {
    "react": {
      // Tells eslint-plugin-react to automatically detect the version of React to use.
      "version": "detect"
    },
    // Tells eslint how to resolve imports
    "import/resolver": {
      "node": {
        "paths": ["src"],
        "extensions": [".js", ".jsx", ".ts", ".tsx"]
      }
    }
  }
}
