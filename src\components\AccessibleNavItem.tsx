import { Link, LinkProps, useColorModeValue } from "@chakra-ui/react";
import React from "react";
import { TouchFeedback } from "./TouchFeedback";

interface AccessibleNavItemProps extends LinkProps {
  isActive?: boolean;
  children: React.ReactNode;
}

export const AccessibleNavItem = ({ 
  isActive, 
  children,
  ...props 
}: AccessibleNavItemProps) => {
  const activeColor = useColorModeValue("brand.primary.500", "brand.primary.300");
  const hoverBg = useColorModeValue("gray.100", "gray.700");

  return (
    <TouchFeedback>
      <Link
        px={3}
        py={2}
        rounded="md"
        color={isActive ? activeColor : undefined}
        _hover={{
          textDecoration: "none",
          bg: hoverBg,
        }}
        role="menuitem"
        aria-current={isActive ? "page" : undefined}
        {...props}
      >
        {children}
      </Link>
    </TouchFeedback>
  );
};