import { PARTNERS } from "#pages/data";
import {
    Box,
    Button,
    Container,
    HStack,
    Heading,
    Icon,
    Image,
    Link,
    SimpleGrid,
    Stack,
    Text,
    VStack,
    useColorModeValue,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";
import { FaExternalLinkAlt } from "react-icons/fa/index.js";

const MotionBox = motion(Box);

export default function PartnerDirectory() {
  const bgColor = useColorModeValue("gray.50", "gray.900");
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box bg={bgColor} minH="100vh" py={16}>
      <Container maxW="container.xl">
        <VStack spacing={12}>
          <Stack spacing={4} textAlign="center" maxW="xl">
            <Heading
              fontSize={{ base: "3xl", md: "4xl" }}
              bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
              bgClip="text"
            >
              Our Technology Partners
            </Heading>
            <Text fontSize="lg" color={useColorModeValue("gray.600", "gray.400")}>
              Leveraging industry-leading solutions to drive your business forward
            </Text>
          </Stack>

          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={8} w="full">
            {PARTNERS.map((partner) => (
              <MotionBox
                key={partner.name}
                whileHover={{ y: -4 }}
                transition={{ duration: 0.2 }}
              >
                <Box
                  bg={cardBg}
                  p={8}
                  borderRadius="xl"
                  borderWidth="1px"
                  borderColor={borderColor}
                  shadow="md"
                >
                  <VStack spacing={6} align="start">
                    <Image
                      src={partner.logo}
                      alt={partner.name}
                      height="60px"
                      objectFit="contain"
                    />
                    <Stack spacing={4}>
                      <Heading size="md">{partner.name}</Heading>
                      <Text>{partner.description}</Text>
                    </Stack>
                    <HStack spacing={4}>
                      <Link href={partner.website} isExternal>
                        <Button
                          leftIcon={<Icon as={FaExternalLinkAlt} />}
                          variant="outline"
                          size="sm"
                        >
                          Visit Website
                        </Button>
                      </Link>
                      {/* {partner.materials && (
                        <Link href={partner.materials} download>
                          <Button
                            leftIcon={<Icon as={FaDownload} />}
                            colorScheme="brand.primary"
                            size="sm"
                          >
                            Download Materials
                          </Button>
                        </Link>
                      )} */}
                    </HStack>
                  </VStack>
                </Box>
              </MotionBox>
            ))}
          </SimpleGrid>
        </VStack>
      </Container>
    </Box>
  );
}