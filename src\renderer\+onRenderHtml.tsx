// https://vike.dev/onRenderHtml
import * as React from "react";
import ReactDOMServer from "react-dom/server";
import { Helmet } from "react-helmet";
import { dangerouslySkipEscape, escapeInject } from "vike/server";
import type { OnRenderHtmlAsync } from "vike/types";
import { PageShell } from "./PageShell";

const onRenderHtml: OnRenderHtmlAsync = async (
  pageContext
): ReturnType<OnRenderHtmlAsync> => {
  const { Page, pageProps } = pageContext;
  // This onRenderHtml() hook only supports SSR, see https://vike.dev/render-modes for how to modify
  // onRenderHtml() to support SPA
  if (!Page)
    throw new Error("My render() hook expects pageContext.Page to be defined");
  const pageHtml = ReactDOMServer.renderToString(
    <PageShell pageContext={pageContext}>
      <Page {...pageProps} />
    </PageShell>
  );
  const helmet = Helmet.renderStatic();
  // See https://vike.dev/head
  const { documentProps } = pageContext.exports;
  const desc =
    (documentProps && documentProps.description) ||
    "Expert IT consulting and cloud solutions provider empowering businesses with innovative technology, managed IT services, and robust security strategies.";
  const documentHtml = escapeInject`<!DOCTYPE html>
    <html ${helmet.htmlAttributes.toString()} lang="en">
      <head>
        ${dangerouslySkipEscape(helmet.meta.toString())}
        <link rel="icon" href="/img/favicon.webp" />
        ${dangerouslySkipEscape(helmet.link.toString())}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com"/>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
        rel="preconnect"
        href="https://fonts.gstatic.com"
        crossorigin="anonymous"
        />
        <link
        href="https://fonts.googleapis.com/css2?family=Exo+2:wght@100;300;400;500;600&family=Odibee+Sans&display=swap"
        rel="stylesheet"
        />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="${desc}" />
        ${dangerouslySkipEscape(helmet.title.toString())}
        </head>
      <body>
        <noscript>
          <style>
            #react-root { display: none !important; }
            .noscript-fallback {
              font-family: 'Exo 2', 'Odibee Sans', Arial, sans-serif;
              max-width: 700px;
              margin: 2rem auto;
              padding: 2.5rem 2rem;
              background: rgba(255,255,255,0.55);
              border-radius: 18px;
              color: #221179;
              box-shadow: 0 8px 32px rgba(56,28,201,0.08);
              position: relative;
              overflow: hidden;
              border: 1.5px solid rgba(56,28,201,0.13);
              backdrop-filter: blur(18px) saturate(1.2);
              -webkit-backdrop-filter: blur(18px) saturate(1.2);

            }
            .noscript-fallback .logo {
              display: flex;
              justify-content: center;
              margin-bottom: 1.5rem;
            }
            .noscript-fallback h1 {
              font-family: 'Odibee Sans', 'Exo 2', Arial, sans-serif;
              font-size: 2.5rem;
              margin-bottom: 0.5rem;
              letter-spacing: 2px;
              background: linear-gradient(90deg, #381CC9 30%, #81052C 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
            .noscript-fallback h2 {
              margin-top: 2rem;
              margin-bottom: 0.5rem;
              font-size: 1.3rem;
              letter-spacing: 1px;
              background: linear-gradient(90deg, #381CC9 30%, #81052C 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
            .noscript-fallback ul {
              padding-left: 1.2em;
              margin-bottom: 1.2em;
            }
            .noscript-fallback li {
              margin-bottom: 0.5em;
              font-size: 1.08em;
            }
            .noscript-fallback a {
              color: #381CC9;
              text-decoration: underline;
              font-weight: 500;
            }
            .noscript-fallback .icon {
              display: inline-block;
              vertical-align: middle;
              margin-right: 0.5em;
              width: 1.2em;
              height: 1.2em;
              /* No background color, just emoji or SVG gradient */
            }
            .noscript-fallback .hero {
              background: rgba(255,255,255,0.7);
              border-radius: 12px;
              padding: 1.5rem 1rem 1rem 1rem;
              margin-bottom: 2rem;
              text-align: center;
              box-shadow: 0 2px 12px #381CC91a;
            }
            .noscript-fallback .hero p {
              color: #381CC9;
            }
            .noscript-fallback .socials {
              margin-top: 1.5rem;
              text-align: center;
            }
            .noscript-fallback .socials a {
              margin: 0 0.5em;
              display: inline-block;
              transition: transform 0.2s;
            }
            .noscript-fallback .socials a:hover {
              transform: scale(1.15);
            }
            .noscript-fallback strong {
              background: linear-gradient(90deg, #381CC9 30%, #81052C 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          </style>
          <div class="noscript-fallback">
            <div class="logo">
              <img width="300px" src="img/logo-no-background.webp"/  >
            </div>
            <div class="hero">
              <h1>TenK Solutions, LLC</h1>
              <p style="font-size:1.2em;">Empowering businesses with innovative IT solutions in the DMV area since 2021.</p>
            </div>
            <h2>Contact Information</h2>
            <ul>
              <li><span class="icon">📞</span><strong>Phone:</strong> <a href="tel:+***********">+****************</a></li>
              <li><span class="icon">✉️</span><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
              <li><span class="icon">📍</span><strong>Location:</strong> Washington D.C. Metropolitan Area</li>
            </ul>
            <h2>Our Services</h2>
            <ul>
              <li>IT Consulting</li>
              <li>Cloud Solutions</li>
              <li>Managed IT Services</li>
              <li>Security Strategies</li>
              <li>And more...</li>
            </ul>
            <h2>Learn More</h2>
            <p>For more information, please contact us by phone or email. This site works best with JavaScript enabled.</p>
            <div class="socials">
              <a href="https://www.linkedin.com/company/tenksolutions" target="_blank" aria-label="LinkedIn">
                <svg class="icon" viewBox="0 0 24 24"><defs><linearGradient id="liGrad" x1="0" y1="0" x2="1" y2="1"><stop offset="0%" stop-color="#381CC9"/><stop offset="100%" stop-color="#81052C"/></linearGradient></defs><path fill="url(#liGrad)" d="M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11 19h-3v-9h3v9zm-1.5-10.28c-.97 0-1.75-.79-1.75-1.75s.78-1.75 1.75-1.75 1.75.79 1.75 1.75-.78 1.75-1.75 1.75zm15.5 10.28h-3v-4.5c0-1.08-.02-2.47-1.5-2.47-1.5 0-1.73 1.17-1.73 2.39v4.58h-3v-9h2.89v1.23h.04c.4-.75 1.38-1.54 2.84-1.54 3.04 0 3.6 2 3.6 4.59v4.72z"/></svg>
              </a>
              <a href="https://www.instagram.com/tenksolutions" target="_blank" aria-label="Instagram">
                <svg class="icon" viewBox="0 0 24 24"><defs><linearGradient id="igGrad" x1="0" y1="0" x2="1" y2="1"><stop offset="0%" stop-color="#381CC9"/><stop offset="100%" stop-color="#81052C"/></linearGradient></defs><path fill="url(#igGrad)" d="M12 2.2c3.2 0 3.584.012 4.85.07 1.17.056 1.97.24 2.43.41.59.22 1.01.48 1.45.92.44.44.7.86.92 1.45.17.46.354 1.26.41 2.43.058 1.266.07 1.65.07 4.85s-.012 3.584-.07 4.85c-.056 1.17-.24 1.97-.41 2.43-.22.59-.48 1.01-.92 1.45-.44.44-.86.7-1.45.92-.46.17-1.26.354-2.43.41-1.266.058-1.65.07-4.85.07s-3.584-.012-4.85-.07c-1.17-.056-1.97-.24-2.43-.41-.59-.22-1.01-.48-1.45-.92-.44-.44-.7-.86-.92-1.45-.17-.46-.354-1.26-.41-2.43C2.212 15.784 2.2 15.4 2.2 12s.012-3.584.07-4.85c.056-1.17.24-1.97.41-2.43.22-.59.48-1.01.92-1.45.44-.44.86-.7 1.45-.92.46-.17 1.26-.354 2.43-.41C8.416 2.212 8.8 2.2 12 2.2zm0-2.2C8.736 0 8.332.012 7.052.07c-1.28.058-2.15.24-2.91.51-.8.28-1.48.66-2.15 1.33-.67.67-1.05 1.35-1.33 2.15-.27.76-.452 1.63-.51 2.91C.012 8.332 0 8.736 0 12c0 3.264.012 3.668.07 4.948.058 1.28.24 2.15.51 2.91.28.8.66 1.48 1.33 2.15.67.67 1.35 1.05 2.15 1.33.76.27 1.63.452 2.91.51C8.332 23.988 8.736 24 12 24c3.264 0 3.668-.012 4.948-.07 1.28-.058 2.15-.24 2.91-.51.8-.28 1.48-.66 2.15-1.33.67-.67 1.05-1.35 1.33-2.15.27-.76.452-1.63.51-2.91.058-1.28.07-1.684.07-4.948 0-3.264-.012-3.668-.07-4.948-.058-1.28-.24-2.15-.51-2.91-.28-.8-.66-1.48-1.33-2.15-.67-.67-1.35-1.05-2.15-1.33-.76-.27-1.63-.452-2.91-.51C15.668.012 15.264 0 12 0z"/><circle cx="12" cy="12" r="3.6"/><circle cx="18.406" cy="5.594" r="1.44"/></svg>
              </a>
              <a href="https://www.facebook.com/tenksolutionsllc" target="_blank" aria-label="Facebook">
                <svg class="icon" viewBox="0 0 24 24"><defs><linearGradient id="fbGrad" x1="0" y1="0" x2="1" y2="1"><stop offset="0%" stop-color="#381CC9"/><stop offset="100%" stop-color="#81052C"/></linearGradient></defs><path fill="url(#fbGrad)" d="M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.408.595 24 1.326 24H12.82v-9.294H9.692v-3.622h3.127V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.408 24 22.674V1.326C24 .592 23.406 0 22.675 0"/></svg>
              </a>
              <a href="https://twitter.com/tenk_solutions" target="_blank" aria-label="X">
                <svg class="icon" viewBox="0 0 24 24"><defs><linearGradient id="xGrad" x1="0" y1="0" x2="1" y2="1"><stop offset="0%" stop-color="#381CC9"/><stop offset="100%" stop-color="#81052C"/></linearGradient></defs><path fill="url(#xGrad)" d="M22.162 0h-20.324c-.995 0-1.838.843-1.838 1.838v20.324c0 .995.843 1.838 1.838 1.838h20.324c.995 0 1.838-.843 1.838-1.838v-20.324c0-.995-.843-1.838-1.838-1.838zm-5.92 7.548l-2.62 3.6 3.74 5.052h-2.13l-2.13-2.88-2.13 2.88h-2.13l3.74-5.052-2.62-3.6h2.13l1.61 2.22 1.61-2.22h2.13z"/></svg>
              </a>
            </div>
          </div>
        </noscript>
        <div id="react-root">${dangerouslySkipEscape(pageHtml)}</div>
      </body>
    </html>`;

  return {
    documentHtml,
    pageContext: {
      pageProps: {
        title: `TenK Solutions, LLC - 
        `,
        // ${
        //   isNotHome ? strippedLocation : "Tech Empowered Achievement"
        // }
        // `,
        description:
          "IT Consultancy firm serving the Metro DMV area (DC, MD, VA) Delivering Innovative IT Solutions for Success",
        image: "https://tenksolutions.com/img/favicon.webp",
        domain: "tenksolutions.com",
      },
      // We can add some `pageContext` here, which is useful if we want to do page redirection https://vike.dev/page-redirection
    },
  };
};
export { onRenderHtml };
