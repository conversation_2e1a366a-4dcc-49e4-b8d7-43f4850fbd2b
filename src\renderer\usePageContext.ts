// `usePageContext` allows us to access `pageContext` in any React component.
// See https://vike.dev/pageContext-anywhereimport React, { useContext } from 'react';
import React from "react";
import { PageContext } from "vike/types";
export const ContextPage = React.createContext<PageContext>(undefined as any);

function usePageContext() {
  const pageContext = React.useContext(ContextPage);
  return pageContext;
}
export default usePageContext;
