import { Icon, IconProps } from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";

const MotionIcon = motion(Icon);

interface AnimatedIconProps extends IconProps {
  isHovered?: boolean;
}

export const AnimatedIcon = ({ isHovered, ...props }: AnimatedIconProps) => {
  return (
    <MotionIcon
      {...props}
      animate={{
        scale: isHovered ? 1.2 : 1,
        rotate: isHovered ? 360 : 0
      }}
      transition={{
        duration: 0.3,
        ease: "easeInOut"
      }}
    />
  );
};