import { FeaturesCard } from "#components/FeaturesCard";
import { OFFERED_SERVICES } from "#pages/data";
import {
  Box,
  Container,
  Fade,
  Flex,
  Heading,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";

// const MotionBox = motion(Box);

// const Feature = ({
//   title,
//   text,
//   icon,
// }: {
//   title: string;
//   text: string;
//   icon: IconType | undefined;
// }) => {
//   return (
//     <MotionBox
//       p={6}
//       bg={useColorModeValue("white", "gray.800")}
//       rounded="xl"
//       shadow="base"
//       whileHover={{ y: -4, boxShadow: "2xl" }}
//       transition={{ duration: 0.2 }}
//       variants={fadeInUp}
//     >
//       <Stack spacing={4}>
//         <Box
//           w={12}
//           h={12}
//           bg={useColorModeValue("brand.primary.50", "brand.primary.900")}
//           rounded="lg"
//           display="flex"
//           alignItems="center"
//           justifyContent="center"
//         >
//           <Icon
//             as={icon}
//             w={6}
//             h={6}
//             color={useColorModeValue("brand.primary.500", "brand.primary.200")}
//           />
//         </Box>
//         <Heading size="md" fontWeight="600">
//           {title}
//         </Heading>
//         <Text color={useColorModeValue("gray.600", "gray.400")}>{text}</Text>
//       </Stack>
//     </MotionBox>
//   );
// };

export const Features = () => {
  const defaultBgColor = useColorModeValue("white", "default");

  return (
    <Box
      as="section"
      aria-label="Key Services"
      px={0}
      py={16}
      bgColor={defaultBgColor}
      w="full"
      mt={0}
    >
      <Container maxW={{ base: "100%", md: "6xl" }} px={{ base: 4, md: 6 }}>
        <Heading
          as="h2"
          textAlign="center"
          fontSize={{ base: "2xl", md: "4xl" }}
          fontWeight="extrabold"
          mb={4}
          letterSpacing="-0.02em"
        >
          <Fade in={true} delay={0.2}>
            Key Services
          </Fade>
        </Heading>
        <Text
          textAlign="center"
          color="gray.500"
          fontSize={{ base: "md", md: "xl" }}
          mb={10}
          maxW="2xl"
          mx="auto"
        >
          Modern businesses need seamless, secure, and scalable technology.
          Explore our core offerings—each designed to help you thrive in a
          digital-first world.
        </Text>
        <Flex wrap="wrap" gap={8} justify="center" align="stretch" gridGap={9}>
          {OFFERED_SERVICES.map(
            ([heading, description, icon, features, img, cta], i) => (
              <Fade in={true} delay={i * 0.5} key={`feature-${i}`}>
                <FeaturesCard
                  key={i}
                  heading={heading}
                  icon={icon}
                  description={description}
                  features={features}
                  image={img}
                  cta={cta}
                />
              </Fade>
            )
          )}
        </Flex>
      </Container>
    </Box>
  );
};
