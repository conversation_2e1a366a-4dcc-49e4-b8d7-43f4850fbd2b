import { Box } from "@chakra-ui/react";
import { motion, useScroll, useTransform } from "framer-motion";
import React, { useState } from "react";

const MotionBox = motion(Box);

interface VideoBackgroundProps {
  src: string;
  children?: React.ReactNode;
  overlay?: boolean;
  overlayColor?: string;
  overlayOpacity?: number;
  parallaxIntensity?: number;
}

export const VideoBackground = ({
  src,
  children,
  overlay = true,
  overlayColor = "black",
  overlayOpacity = 0.5,
  parallaxIntensity = 100
}: VideoBackgroundProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, parallaxIntensity]);

  return (
    <Box position="relative" overflow="hidden" minH="100vh">
      <MotionBox
        position="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        style={{ y }}
      >
        <video
          autoPlay
          muted
          loop
          playsInline
          onLoadedData={() => setIsLoaded(true)}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
            opacity: isLoaded ? 1 : 0,
            transition: "opacity 0.5s ease-in-out"
          }}
        >
          <source src={src} type="video/mp4" />
        </video>
      </MotionBox>

      {overlay && (
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg={overlayColor}
          opacity={overlayOpacity}
          transition="opacity 0.3s ease-in-out"
          _hover={{ opacity: overlayOpacity - 0.1 }}
        />
      )}

      <Box
        position="relative"
        zIndex={1}
        minH="100vh"
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
      >
        {children}
      </Box>
    </Box>
  );
};