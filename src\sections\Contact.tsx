"use client";

import { AccessibleForm } from "#components/AccessibleForm";
import { fadeInUp, staggerChildren } from "#utils/animationVariants";
import {
  Box,
  Container,
  Heading,
  Icon,
  SimpleGrid,
  Text,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";
import { IconType } from "react-icons";
import { FaEnvelope, FaMapMarkerAlt, FaPhone } from "react-icons/fa/index.js";

const MotionBox = motion(Box);

const ContactInfo = ({
  icon,
  title,
  content,
}: {
  icon: IconType;
  title: string;
  content: string;
}) => {
  const bgColor = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("brand.primary.500", "brand.primary.300");
  const iconColor = useColorModeValue("gray.600", "gray.400");
  return (
    <MotionBox
      variants={fadeInUp}
      display="flex"
      alignItems="center"
      p={4}
      rounded="lg"
      bg={bgColor}
      shadow="md"
      _hover={{ transform: "translateY(-2px)", shadow: "lg" }}
      transition="all 0.3s"
    >
      <Icon as={icon} w={6} h={6} color={iconColor} mr={4} />
      <VStack align="start" spacing={1}>
        <Text fontWeight="bold">{title}</Text>
        <Text color={textColor}>{content}</Text>
      </VStack>
    </MotionBox>
  );
};

const formFields = [
  { name: "name", label: "Name", type: "text" as const, required: true },
  { name: "email", label: "Email", type: "email" as const, required: true },
  {
    name: "message",
    label: "Message",
    type: "textarea" as const,
    required: true,
    minLength: 10,
  },
];

export const ContactSection = () => {
  const handleSubmit = async (data: Record<string, string>) => {
    // Implement form submission logic
    console.log("Form submitted:", data);
  };

  const boxBgColor = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("gray.600", "gray.400");
  return (
    <Box
      as="section"
      py={20}
      bg={useColorModeValue("gray.50", "gray.900")}
      id="contact"
    >
      <Container maxW="container.xl">
        <MotionBox
          variants={staggerChildren}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <VStack spacing={12}>
            <VStack spacing={4} textAlign="center">
              <Heading
                fontSize={{ base: "3xl", md: "4xl" }}
                bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
                bgClip="text"
              >
                Get in Touch
              </Heading>
              <Text fontSize="lg" color={textColor} maxW="2xl">
                Ready to transform your IT infrastructure? Contact us for a free
                consultation.
              </Text>
            </VStack>

            <SimpleGrid
              columns={{ base: 1, md: 2 }}
              spacing={8}
              w="full"
              maxW="container.lg"
              mx="auto"
            >
              <VStack spacing={8} align="start">
                <ContactInfo
                  icon={FaPhone}
                  title="Call Us"
                  content="+****************"
                />
                <ContactInfo
                  icon={FaEnvelope}
                  title="Email Us"
                  content="<EMAIL>"
                />
                <ContactInfo
                  icon={FaMapMarkerAlt}
                  title="Visit Us"
                  content="Washington D.C. Metropolitan Area"
                />
              </VStack>

              <MotionBox
                variants={fadeInUp}
                bg={boxBgColor}
                p={{ base: 6, md: 8 }}
                rounded="xl"
                shadow="lg"
              >
                <AccessibleForm
                  fields={formFields}
                  onSubmit={handleSubmit}
                  submitText="Send Message"
                />
              </MotionBox>
            </SimpleGrid>
          </VStack>
        </MotionBox>
      </Container>
    </Box>
  );
};
