// components/ProblemSolver.tsx
import { PROBLEM_SOLUTIONS, ProblemSolution } from "#pages/data";
import {
  <PERSON>ge,
  Box,
  <PERSON>ton,
  Collapse,
  Container,
  Grid,
  GridItem,
  Heading,
  HStack,
  Icon,
  Modal,
  ModalContent,
  ModalOverlay,
  Stack,
  Tab,
  TabList,
  Tabs,
  Text,
  Tooltip,
  useDisclosure,
} from "@chakra-ui/react";
import { AnimatePresence, motion } from "framer-motion";
import React, { useState } from "react";
import { FiArrowRight } from "react-icons/fi/index.js";

export const ProblemSolver: React.FC = () => {
  const [activeSolution, setActiveSolution] = useState<ProblemSolution>(
    PROBLEM_SOLUTIONS[0]
  );
  const [expandedDetail, setExpandedDetail] = useState<string | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const MotionBox = motion(Box);

  return (
    <Box as="section" py={{ base: 16, md: 24 }}>
      <Container maxW="container.xl">
        <Stack spacing={12}>
          {/* Previous header content remains the same */}

          <Grid templateColumns={{ base: "1fr", lg: "300px 1fr" }} gap={8}>
            <GridItem>
              <Tabs orientation="vertical" variant="soft-rounded">
                <TabList>
                  {PROBLEM_SOLUTIONS.map((solution) => (
                    <Tab
                      key={solution.problem}
                      onClick={() => setActiveSolution(solution)}
                      _selected={{ bg: "blue.500", color: "white" }}
                    >
                      <Stack>
                        <Text>{solution.problem}</Text>
                        <Badge size="sm" colorScheme="blue">
                          {solution.estimatedTimeFrame}
                        </Badge>
                      </Stack>
                    </Tab>
                  ))}
                </TabList>
              </Tabs>
            </GridItem>

            <GridItem>
              <AnimatePresence mode="wait">
                <MotionBox
                  key={activeSolution.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Stack spacing={6}>
                    <Stack
                      direction="row"
                      justify="space-between"
                      align="center"
                    >
                      <Heading size="lg">{activeSolution.title}</Heading>
                      <HStack spacing={2}>
                        {activeSolution.technologies.map((tech) => (
                          <Tooltip key={tech} label={`Built with ${tech}`}>
                            <Badge colorScheme="gray">{tech}</Badge>
                          </Tooltip>
                        ))}
                      </HStack>
                    </Stack>

                    <Text fontSize="lg">{activeSolution.description}</Text>

                    <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                      {activeSolution.recommendations.map((rec) => (
                        <Box
                          key={rec.text}
                          p={4}
                          borderRadius="lg"
                          border="1px"
                          borderColor="gray.200"
                          cursor="pointer"
                          onClick={() => setExpandedDetail(rec.text)}
                          _hover={{ bg: "gray.50" }}
                        >
                          <Stack direction="row" spacing={4}>
                            <Icon as={rec.icon} boxSize={6} color="blue.500" />
                            <Stack spacing={1}>
                              <Text fontWeight="bold">{rec.text}</Text>
                              <Collapse in={expandedDetail === rec.text}>
                                <Text fontSize="sm" color="gray.600">
                                  {rec.details}
                                </Text>
                                {rec.metrics && (
                                  <HStack mt={2}>
                                    <Text fontSize="xl" fontWeight="bold">
                                      {rec.metrics.value}
                                    </Text>
                                    <Text fontSize="sm" color="gray.600">
                                      {rec.metrics.label}
                                    </Text>
                                  </HStack>
                                )}
                              </Collapse>
                            </Stack>
                          </Stack>
                        </Box>
                      ))}
                    </Grid>

                    {activeSolution.relevantCaseStudy && (
                      <Box
                        p={4}
                        bg="blue.50"
                        borderRadius="lg"
                        cursor="pointer"
                        onClick={onOpen}
                      >
                        <Stack
                          direction="row"
                          justify="space-between"
                          align="center"
                        >
                          <Stack>
                            <Text fontWeight="bold">Success Story</Text>
                            <Text>
                              {activeSolution.relevantCaseStudy.company}
                            </Text>
                          </Stack>
                          <Button variant="link" colorScheme="blue">
                            View Case Study
                          </Button>
                        </Stack>
                      </Box>
                    )}

                    <Button
                      size="lg"
                      colorScheme="blue"
                      rightIcon={<Icon as={FiArrowRight} />}
                    >
                      {activeSolution.ctaText}
                    </Button>
                  </Stack>
                </MotionBox>
              </AnimatePresence>
            </GridItem>
          </Grid>
        </Stack>
      </Container>

      {/* Case Study Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>{/* Add case study content here */}</ModalContent>
      </Modal>
    </Box>
  );
};
