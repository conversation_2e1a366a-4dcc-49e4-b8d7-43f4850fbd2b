import { Button } from "@chakra-ui/react";
import { motion, useScroll, useTransform } from "framer-motion";
import React from "react";

const MotionButton = motion(Button);

export const FloatingCTA = () => {
  const { scrollYProgress } = useScroll();
  const opacity = useTransform(scrollYProgress, [0, 0.2], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 0.2], [100, 0]);

  return (
    <MotionButton
      position="fixed"
      bottom="4"
      left="50%"
      transform="translateX(-50%)"
      zIndex="sticky"
      style={{ opacity, y }}
      size="lg"
      bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
      color="white"
      _hover={{
        transform: "translateX(-50%) translateY(-2px)",
        boxShadow: "lg",
      }}
      onClick={() => {
        document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" });
      }}
    >
      Get Free Consultation
    </MotionButton>
  );
};