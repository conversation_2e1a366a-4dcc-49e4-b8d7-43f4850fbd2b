"use client";

import { StarIcon } from "@chakra-ui/icons";
import {
  Box,
  Container,
  Flex,
  Heading,
  Icon,
  ScaleFade,
  Stack,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import React, { useEffect, useState } from "react";
import {
  FaHandshake,
  FaLaptopCode,
  FaNetworkWired,
  FaUserShield,
} from "react-icons/fa/index.js";

const WHAT_WE_DO = [
  {
    icon: FaHandshake,
    headline: "True Customer Advocacy",
    sub: "Impartial, trusted, and always on your side—no carrier bias, just your best deal.",
    color: "blue.400",
    border: "linear-gradient(90deg, #7EB2DD 0%, #6830de 100%)",
  },
  {
    icon: StarIcon,
    headline: "Instant Quotes from 200+ Providers",
    sub: "Get rapid, unbiased price comparisons for business internet, fiber, UCaaS, CCaaS, and colocation.",
    color: "yellow.400",
    border: "linear-gradient(90deg, #F6E278 0%, #FF420E 100%)",
  },
  {
    icon: FaNetworkWired,
    headline: "National Reach, Local Trust",
    sub: "DMV-based, but delivering solutions and support nationwide.",
    color: "purple.400",
    border: "linear-gradient(90deg, #b89cef 0%, #028C6A 100%)",
  },
  {
    icon: FaUserShield,
    headline: "Exclusive, Premium Managed IT",
    sub: "High-touch, hands-on IT management—offered only to select clients.",
    color: "red.400",
    border: "linear-gradient(90deg, #F93943 0%, #3D405B 100%)",
  },
  {
    icon: FaLaptopCode,
    headline: "Web & Automation Engineering",
    sub: "Custom internal tools and web presence optimization for your business.",
    color: "green.400",
    border: "linear-gradient(90deg, #80BD9E 0%, #b89cef 100%)",
  },
];

const WhatWeDo = () => {
  const [loaded, setLoaded] = useState(false);
  const cardBg = useColorModeValue("whiteAlpha.80", "blackAlpha.700");
  const cardText = useColorModeValue("gray.700", "gray.200");
  // const theme = useTheme();
  useEffect(() => {
    setLoaded(true);
    return () => setLoaded(false);
  }, []);

  return (
    <Stack spacing={8} as={Container} maxW={"container.lg"} py={12} px={4}>
      <Heading
        as="h1"
        fontSize={{ base: "2xl", lg: "4xl" }}
        fontWeight="extrabold"
        textAlign="center"
        mb={2}
        letterSpacing="-0.02em"
      >
        What We Do
      </Heading>
      <Text
        fontSize={{ base: "md", md: "xl" }}
        color={useColorModeValue("gray.600", "gray.300")}
        textAlign="center"
        mb={6}
      >
        Get instant, unbiased business connectivity and IT solutions—trusted by
        local relationships, delivered nationwide.
      </Text>
      <Flex
        wrap="wrap"
        gap={6}
        justify="center"
        align="stretch"
        direction={{ base: "column", md: "row" }}
      >
        {WHAT_WE_DO.map((item, i) => (
          <ScaleFade
            in={loaded}
            initialScale={0.7}
            transition={{ enter: { delay: i * 0.18, duration: 0.7 } }}
            key={item.headline}
          >
            <Box
              bg={cardBg}
              borderRadius="2xl"
              boxShadow="2xl"
              p={{ base: 5, md: 7 }}
              minW={{ base: "auto", md: 64 }}
              maxW={"sm"}
              borderWidth={2}
              borderStyle="solid"
              // borderW={item.border + " 1"}
              display="flex"
              flexDirection="column"
              alignItems="center"
              backdropFilter="auto"
              backdropBlur="14px"
              transition="box-shadow 0.3s, transform 0.3s"
              _hover={{
                boxShadow: "xl",
                transform: "translateY(-4px) scale(1.03)",
              }}
            >
              <Icon as={item.icon} boxSize={10} color={item.color} mb={3} />
              <Heading
                as="h2"
                fontSize={{ base: "lg", md: "xl" }}
                fontWeight="bold"
                mb={2}
                textAlign="center"
                letterSpacing="-0.01em"
              >
                {item.headline}
              </Heading>
              <Text
                fontSize={{ base: "sm", md: "md" }}
                color={cardText}
                textAlign="center"
              >
                {item.sub}
              </Text>
            </Box>
          </ScaleFade>
        ))}
      </Flex>
    </Stack>
  );
};

export default WhatWeDo;
