"use client";

import { QuestionCard } from "#components/QuestionCard";
import { LEAD_QUESTIONS } from "#pages/data";
import {
  Box,
  Flex,
  Heading,
  IconButton,
  Text,
  useBreakpointValue,
} from "@chakra-ui/react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa/index.js";

const QuestionnaireSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const questions = LEAD_QUESTIONS;
  const slidesCount = questions.length;
  const cardsPerView = useBreakpointValue({ base: 1, md: 2 }) || 1;
  const maxIndex = Math.max(0, slidesCount - cardsPerView);
  const SLIDES_INTERVAL_TIME = 8000;
  const carouselRef = useRef<HTMLDivElement>(null);
  // For drag/swipe
  const dragState = useRef<{
    startX: number;
    dragging: boolean;
  }>({ startX: 0, dragging: false });

  // Slide to next/prev, clamp to maxIndex
  const goTo = useCallback(
    (idx: number) => {
      if (slidesCount <= cardsPerView) return setCurrentSlide(0);
      if (idx < 0) setCurrentSlide(maxIndex);
      else if (idx > maxIndex) setCurrentSlide(0);
      else setCurrentSlide(idx);
    },
    [cardsPerView, maxIndex, slidesCount]
  );

  const nextSlide = useCallback(
    () => goTo(currentSlide + 1),
    [currentSlide, goTo]
  );

  const prevSlide = () => goTo(currentSlide - 1);

  // Auto-advance
  useEffect(() => {
    if (isPaused || slidesCount <= cardsPerView) return;
    const timer = setInterval(nextSlide, SLIDES_INTERVAL_TIME);
    return () => clearInterval(timer);
  }, [currentSlide, isPaused, maxIndex, cardsPerView, slidesCount, nextSlide]);

  // Drag/swipe handlers
  const onDragStart = (e: React.MouseEvent | React.TouchEvent) => {
    if (slidesCount <= cardsPerView) return;
    setIsPaused(true);
    dragState.current.dragging = true;
    dragState.current.startX =
      "touches" in e ? e.touches[0].clientX : e.clientX;
  };
  const onDragMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!dragState.current.dragging) return;
    const x = "touches" in e ? e.touches[0].clientX : e.clientX;
    const dx = x - dragState.current.startX;
    if (Math.abs(dx) > 60) {
      dragState.current.dragging = false;
      if (dx > 0) prevSlide();
      else nextSlide();
      setTimeout(() => setIsPaused(false), 1000);
    }
  };
  const onDragEnd = () => {
    dragState.current.dragging = false;
    setTimeout(() => setIsPaused(false), 1000);
  };

  // Calculate transform for sliding
  const slideTransform =
    slidesCount <= cardsPerView
      ? { display: "flex", justifyContent: "center", width: "100%" }
      : {
          display: "flex",
          transition: "transform 0.6s cubic-bezier(.4,0,.2,1)",
          transform: `translateX(-${currentSlide * (100 / cardsPerView)}%)`,
          // width: `${(slidesCount * 100) / cardsPerView}%`,
          widht: "100%",
        };

  return (
    <Box
      as="section"
      py={16}
      px={{ base: 2, md: 10 }}
      position="relative"
      zIndex={1}
      overflowX="hidden" // Prevent horizontal scrollbars
    >
      <Heading
        as="h2"
        fontSize={{ base: "2xl", md: "3xl" }}
        fontWeight="extrabold"
        textAlign="center"
        mb={8}
        letterSpacing="-0.02em"
        userSelect="none"
      >
        Find the Right Solution Instantly
      </Heading>
      <Text
        textAlign="center"
        fontSize={{ base: "lg", md: "xl" }}
        color="gray.500"
        mb={10}
        userSelect="none"
      >
        Answer a few quick questions to get matched with the best business
        Internet, voice, cloud, or security solution—no carrier bias, no sales
        pressure. Serving the DMV and businesses nationwide.
      </Text>
      <Flex
        // w="100vw"
        maxW={{ base: "100%", md: "5xl" }}
        py={3}
        mx="auto"
        align="center"
        position="relative"
        overflow="hidden" // Hide overflow for the slider
      >
        <IconButton
          aria-label="Previous"
          icon={<FaChevronLeft />}
          // color={"red.200"}
          onClick={() => {
            setIsPaused(true);
            prevSlide();
            setTimeout(() => setIsPaused(false), 1000);
          }}
          variant="ghost"
          size="lg"
          display={{
            base: "none",
            md: slidesCount > cardsPerView ? "flex" : "none",
          }}
          zIndex={2}
        />
        <Box
          ref={carouselRef}
          w="full"
          // px={{ base: 2, md: 8 }} // extra padding for card shadows
          onMouseDown={onDragStart}
          onMouseMove={onDragMove}
          onMouseUp={onDragEnd}
          onMouseLeave={onDragEnd}
          onTouchStart={onDragStart}
          onTouchMove={onDragMove}
          onTouchEnd={onDragEnd}
          cursor={slidesCount > cardsPerView ? "grab" : "default"}
        >
          <Flex {...slideTransform} gap={8} align="stretch">
            {questions.map((q, i) => (
              <Box
                key={`slide-${i}`}
                flex={`0 0 ${100 / cardsPerView}%`}
                boxSizing="border-box"
                display="flex"
                alignItems="stretch"
                justifyContent="center"
                px={0}
              >
                <QuestionCard
                  question={q.question}
                  answer={q.answer}
                  cta={q.cta}
                />
              </Box>
            ))}
          </Flex>
        </Box>
        <IconButton
          aria-label="Next"
          icon={<FaChevronRight />}
          onClick={() => {
            setIsPaused(true);
            nextSlide();
            setTimeout(() => setIsPaused(false), 1000);
          }}
          variant="ghost"
          size="lg"
          display={{
            base: "none",
            md: slidesCount > cardsPerView ? "flex" : "none",
          }}
          zIndex={2}
        />
      </Flex>
    </Box>
  );
};

export default QuestionnaireSlider;
