import {
  <PERSON>,
  <PERSON><PERSON>,
  Fade,
  <PERSON>ing,
  Icon,
  Link,
  Text,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";
import { FaQuestionCircle } from "react-icons/fa/index.js";

type QCardProps = { question: string; answer: string; cta: string };
export const QuestionCard = ({ question, answer, cta }: QCardProps) => {
  const cardBg = useColorModeValue("whiteAlpha.80", "blackAlpha.700");
  const cardBorder = useColorModeValue("whiteAlpha.400", "whiteAlpha.200");
  const cardText = useColorModeValue("gray.700", "gray.200");
  return (
    <Fade in={true}>
      <VStack
        p={{ base: 6, sm: 10 }}
        borderRadius={"2xl"}
        border="1.5px solid"
        borderColor={cardBorder}
        bg={cardBg}
        boxShadow="2xl"
        textAlign={"center"}
        spacing={{ base: 4, lg: 8 }}
        backdropFilter="auto"
        backdropBlur="18px"
        color={cardText}
        // minH="340px"
        maxH="lg"
        w="lg"
        mx="auto"
        position="relative"
      >
        <Box display="flex" alignItems="center" gap={3} mb={1}>
          <Icon as={FaQuestionCircle} color="blue.400" boxSize={7} />
          <Heading
            fontSize={{ base: "2xl", lg: "3xl" }}
            textAlign="left"
            flex={1}
            userSelect="none"
          >
            {question}
          </Heading>
        </Box>
        <Text
          textAlign={"left"}
          fontFamily={"'Open Sans', sans-serif"}
          fontSize={{ base: "lg", lg: "xl" }}
          my={3}
          userSelect="none"
        >
          {answer}
        </Text>
        <Link href={"/contact-us"} title={`Contact TenK Solutions: ${cta}`}>
          <Button
            rounded={"full"}
            colorScheme="blue"
            size="lg"
            fontWeight="bold"
          >
            {cta}
          </Button>
        </Link>
      </VStack>
    </Fade>
  );
};
