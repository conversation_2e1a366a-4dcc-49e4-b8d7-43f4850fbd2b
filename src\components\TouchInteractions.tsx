import { Box } from "@chakra-ui/react";
import { PanInfo, motion, useMotionValue, useTransform } from "framer-motion";
import React from "react";

const MotionBox = motion(Box);

interface TouchInteractionsProps {
  children: React.ReactNode;
  dragConstraints?: number;
  swipeThreshold?: number;
  onSwipe?: (direction: "left" | "right") => void;
}

export const TouchInteractions = ({
  children,
  dragConstraints = 100,
  swipeThreshold = 50,
  onSwipe,
}: TouchInteractionsProps) => {
  const x = useMotionValue(0);
  const scale = useTransform(
    x,

    [-dragConstraints, 0, dragConstraints],
    [0.95, 1, 0.95]
  );

  const handleDragEnd = (_: any, info: PanInfo) => {
    if (Math.abs(info.offset.x) > swipeThreshold && onSwipe) {
      onSwipe(info.offset.x > 0 ? "right" : "left");
    }
  };

  return (
    <MotionBox
      drag="x"
      dragConstraints={{ left: -dragConstraints, right: dragConstraints }}
      dragElastic={0.2}
      onDragEnd={handleDragEnd}
      style={{ x, scale }}
      whileTap={{ cursor: "grabbing" }}
    >
      {children}
    </MotionBox>
  );
};
