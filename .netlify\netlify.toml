functionsDirectory = "C:\\Users\\<USER>\\SITES\\tenKSolutions\\src\\netlify\\functions"
functionsDirectoryOrigin = "config"
headers = []
redirects = []

[dev]
targetPort = 3000.0

[functions]

[functions.node_version]
runtime = "nodejs20.x"

[functions."*"]
node_bundler = "esbuild"

[[plugins]]
origin = "ui"
package = "@netlify/plugin-lighthouse"
pinned_version = "5"

[plugins.inputs]

[build]
publish = "C:\\Users\\<USER>\\SITES\\tenKSolutions\\dist"
publishOrigin = "ui"
commandOrigin = "ui"
command = "yarn run build"
functions = "C:\\Users\\<USER>\\SITES\\tenKSolutions\\src\\netlify\\functions"

[build.environment]

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]