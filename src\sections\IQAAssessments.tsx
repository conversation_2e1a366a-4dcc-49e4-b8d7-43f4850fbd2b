import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  SimpleGrid,
  Text,
  VStack,
} from "@chakra-ui/react";
import React from "react";

const IQA_LIST = [
  {
    name: "Backup & Disaster Recovery",
    url: "https://10k.gopathfinder.net/#/iqa-form/backupdisasterrecovery?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Colocation",
    url: "https://10k.gopathfinder.net/#/iqa-form/colocation?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  //   {
  //     name: "Connectivity",
  //     url: "https://10k.gopathfinder.net/#/iqa-form/connectivity?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  //   },
  //   {
  //     name: "Connectivity Lite",
  //     url: "https://10k.gopathfinder.net/#/iqa-form/connectivitylite?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  //   },
  {
    name: "Contact Center as a Service",
    url: "https://10k.gopathfinder.net/#/iqa-form/contactcenterasaservice?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "CX AI",
    url: "https://10k.gopathfinder.net/#/iqa-form/cxai?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Desktop as a Service",
    url: "https://10k.gopathfinder.net/#/iqa-form/desktopasaservice?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  //   {
  //     name: "Discovery",
  //     url: "https://10k.gopathfinder.net/#/iqa-form/discovery?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  //   },
  {
    name: "Email & Productivity",
    url: "https://10k.gopathfinder.net/#/iqa-form/emailproductivity?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  //   {
  //     name: "General Inquiry",
  //     url: "https://10k.gopathfinder.net/#/iqa-form/generalinquiry?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  //   },
  {
    name: "Help Desk",
    url: "https://10k.gopathfinder.net/#/iqa-form/helpdesk?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Hosted Fax",
    url: "https://10k.gopathfinder.net/#/iqa-form/hostedfax?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Infrastructure as a Service",
    url: "https://10k.gopathfinder.net/#/iqa-form/infrastructureasaservice?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Managed Public Cloud",
    url: "https://10k.gopathfinder.net/#/iqa-form/managedpubliccloud?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Managed Services",
    url: "https://10k.gopathfinder.net/#/iqa-form/managedservices?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Mobility",
    url: "https://10k.gopathfinder.net/#/iqa-form/mobility?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Penetration Testing",
    url: "https://10k.gopathfinder.net/#/iqa-form/penetrationtesting?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Physical Security",
    url: "https://10k.gopathfinder.net/#/iqa-form/physicalsecurity?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "SD-WAN & SASE",
    url: "https://10k.gopathfinder.net/#/iqa-form/sdwansase?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Security",
    url: "https://10k.gopathfinder.net/#/iqa-form/security?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  //   {
  //     name: "SIP Trunking",
  //     url: "https://10k.gopathfinder.net/#/iqa-form/siptrunking?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  //   },
  {
    name: "Telecom Management",
    url: "https://10k.gopathfinder.net/#/iqa-form/telecommanagement?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "UCaaS",
    url: "https://10k.gopathfinder.net/#/iqa-form/ucaas?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  //   {
  //     name: "UCaaS: Microsoft Teams",
  //     url: "https://10k.gopathfinder.net/#/iqa-form/ucaasmicrosoftteams?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  //   },
  {
    name: "VMware",
    url: "https://10k.gopathfinder.net/#/iqa-form/vmware?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
  {
    name: "Wireless & IoT",
    url: "https://10k.gopathfinder.net/#/iqa-form/wirelessiot?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  },
];

const IQAAssessments = () => (
  <Box as="section" py={{ base: 8, md: 16 }} px={{ base: 2, md: 8 }}>
    <VStack spacing={4} mb={8}>
      <Heading as="h2" size="lg" textAlign="center">
        Instant Quote Assessments (IQA)
      </Heading>
      <Text
        fontSize={{ base: "md", md: "lg" }}
        color="gray.600"
        textAlign="center"
      >
        Get tailored recommendations from our sales engineers—just fill out a
        quick IQA and receive a response within 24 hours. Choose the assessment
        that fits your needs and start your journey to better solutions.
      </Text>
    </VStack>
    <SimpleGrid columns={{ base: 1, sm: 2, md: 3 }} spacing={6}>
      {IQA_LIST.map((iqa) => (
        <Box
          key={iqa.name}
          p={6}
          borderWidth={1}
          borderRadius="xl"
          boxShadow="md"
        >
          <Heading as="h3" size="md" mb={2}>
            {iqa.name}
          </Heading>
          <Button
            as={Link}
            href={iqa.url}
            variant="solid"
            w="full"
            target="_blank"
            rel="noopener noreferrer"
            mt={2}
          >
            Start Assessment
          </Button>
        </Box>
      ))}
    </SimpleGrid>
  </Box>
);

export default IQAAssessments;
