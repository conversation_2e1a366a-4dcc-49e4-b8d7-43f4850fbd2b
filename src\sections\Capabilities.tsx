import {
  Box,
  Button,
  Heading,
  Link,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";

const Capabilities = () => {
  const bgColor = useColorModeValue(
    "colors.brand.primary.50",
    "colors.brand.primary.900"
  );
  return (
    <Box
      p={8}
      width="full"
      textAlign={"center"}
      bgColor={bgColor}
      borderRadius={"sm"}
      bgImage={'https://picsum.photos/id/180/800/300'}
      bgBlendMode={'soft-light'}
      bgPosition={'cover'}
    >
      <Heading as="h3" fontSize="xl" my="7" textAlign={"center"}>
        Learn how our team's expertise can take your enterprise technology to
        the next level.
      </Heading>

      <Link href="/files/Capability-Statement.pdf">
        <Button variant={'solid'} p={7} colorScheme="colors.brand.primary">
          Download Capabilities Statement
        </Button>
      </Link>
    </Box>
  );
};

export default Capabilities;
