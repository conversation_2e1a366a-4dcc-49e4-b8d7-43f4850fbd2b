// Create a new Partner Directory page structure
import { Box, Flex, Heading, Link, Text } from '@chakra-ui/react';
import React from 'react';
import { PARTNERS } from './data';

const PartnerDirectory = () => (
  <Box>
    <Heading as="h1">Partner Directory</Heading>
    <Flex wrap="wrap" justify="center">
      {PARTNERS.map((partner) => (
        <Box key={partner.name} p={4} borderWidth={1} borderRadius="md">
          <Heading as="h3" size="md">{partner.name}</Heading>
          <Text>{partner.website}</Text>
          <Link href={partner.website}>View Materials</Link>
        </Box>
      ))}
    </Flex>
  </Box>
);

export default PartnerDirectory;