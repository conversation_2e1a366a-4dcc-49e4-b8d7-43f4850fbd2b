import { CaseStudyCard } from "#components/CaseStudyCard";
import { CASE_STUDIES } from "#pages/data";
import { Box, Heading, SimpleGrid, Text, useColorModeValue } from "@chakra-ui/react";
import * as React from "react";

export const CaseStudyGallery: React.FC = () => {
  const bgColor = useColorModeValue("colors.brand.primary.50", "colors.brand.primary.900");
  return (
    <Box p={10}>
      <Heading fontSize="2xl" mb={6} as={"h1"}>
        Our Proven Approach to Strategic IT Solutions
      </Heading>
      <Text mb={10} fontSize="large" p={6} bg={bgColor} borderRadius={'md'} dropShadow={'md'}>
        Every project at TenK Solutions, LLC is handled with precision, insight, and
        a clear understanding of each client's unique needs. Our process begins
        with in-depth assessments, understanding client pain points, and
        aligning solutions with organizational goals. Through meticulous
        planning, strategic execution, and robust follow-up, we craft IT
        solutions that drive measurable impact. Whether implementing cloud
        migrations, optimizing networks, or enhancing security protocols, each
        case study below reflects our commitment to delivering tailored,
        high-value outcomes for every client.
      </Text>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8}>
        {CASE_STUDIES.map(
          ({ title, imgSrc, companyDesc, summary, companyName, uri }, index) => (
            <CaseStudyCard
              key={index}
              title={title}
              imgSrc={imgSrc}
              companyName={companyName}
              companyDesc={companyDesc}
              summary={summary}
              uri={uri}
            />
          )
        )}
      </SimpleGrid>
    </Box>
  );
};
