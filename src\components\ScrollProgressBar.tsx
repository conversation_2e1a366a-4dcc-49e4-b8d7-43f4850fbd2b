import { Box } from "@chakra-ui/react";
import { motion, useScroll, useSpring } from "framer-motion";
import React from "react";

const MotionBox = motion(Box);

export const ScrollProgressBar = () => {
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  return (
    <MotionBox
      position="fixed"
      top={0}
      left={0}
      right={0}
      height="2px"
      bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
      transformOrigin="0%"
      style={{ scaleX }}
      zIndex="banner"
    />
  );
};