import {
    Box,
    Container,
    Flex,
    <PERSON>,
    <PERSON>ack,
    useColorModeValue,
} from "@chakra-ui/react";
import { motion, useScroll } from "framer-motion";
import React, { ReactNode, useEffect, useState } from "react";

const MotionBox = motion(Box);

const NavLink = ({
  children,
  href,
  isActive,
}: {
  children: ReactNode;
  href: string;
  isActive: boolean;
}) => (
  <Link
    px={2}
    py={1}
    rounded="md"
    position="relative"
    _hover={{ textDecoration: "none" }}
    onClick={(e) => {
      e.preventDefault();
      document.querySelector(href)?.scrollIntoView({ behavior: "smooth" });
    }}
  >
    {children}
    <MotionBox
      position="absolute"
      bottom="-2px"
      left={0}
      right={0}
      height="2px"
      bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
      animate={{ scaleX: isActive ? 1 : 0 }}
      transition={{ duration: 0.2 }}
    />
  </Link>
);

export default function Navbar() {
  const [activeSection, setActiveSection] = useState("");
  const { scrollY } = useScroll();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    return scrollY.onChange((latest) => {
      setIsScrolled(latest > 50);
    });
  }, [scrollY]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
          }
        });
      },
      { threshold: 0.5 }
    );

    document.querySelectorAll("section[id]").forEach((section) => {
      observer.observe(section);
    });

    return () => observer.disconnect();
  }, []);

  return (
    <MotionBox
      position="fixed"
      top={0}
      left={0}
      right={0}
      zIndex="sticky"
      animate={{
        backgroundColor: isScrolled
          ? useColorModeValue("white", "gray.800")
          : "transparent",
        boxShadow: isScrolled ? "md" : "none",
      }}
      transition={{ duration: 0.2 }}
    >
      <Container maxW="container.xl">
        <Flex h={16} alignItems="center" justifyContent="space-between">
          <Stack direction="row" spacing={4}>
            <NavLink href="#home" isActive={activeSection === "home"}>
              Home
            </NavLink>
            <NavLink href="#services" isActive={activeSection === "services"}>
              Services
            </NavLink>
            <NavLink href="#partners" isActive={activeSection === "partners"}>
              Partners
            </NavLink>
            <NavLink href="#contact" isActive={activeSection === "contact"}>
              Contact
            </NavLink>
          </Stack>
        </Flex>
      </Container>
    </MotionBox>
  );
}
