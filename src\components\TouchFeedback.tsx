import { Box, BoxProps } from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";

const MotionBox = motion(Box);

interface TouchFeedbackProps extends BoxProps {
  children: React.ReactNode;
  feedbackScale?: number;
}

export const TouchFeedback = ({ 
  children, 
  feedbackScale = 0.95,
  ...props 
}: TouchFeedbackProps) => {
  return (
    <MotionBox
      whileTap={{ scale: feedbackScale }}
      transition={{ duration: 0.1 }}
      {...props}
    >
      {children}
    </MotionBox>
  );
};