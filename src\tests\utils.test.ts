//use react-testing-library to create a test for the 'slugify' function
import {
  isNotPrivacyString,
  slugify,
  titleize,
  toTitleCase,
} from "#utils/utils";
import { expect, test } from "vitest";

test("slugify", () => {
  expect(slugify("Hello World")).toBe("hello-world");
});

test("toTitleCase", () => {
  expect(toTitleCase("hello world")).toBe("Hello World");
});
test("isPrivacyLink", () => {
  expect(isNotPrivacyString("privacy")).toBe(true);
  expect(isNotPrivacyString("Privacy")).toBe(false);
});

test("titleize", () => {
  expect(titleize("hello-world")).toBe("Hello World");
  expect(titleize("hello-world-123")).toBe("Hello World 123");
  expect(titleize("hello world")).toBe("");
  expect(titleize("hello_world")).toBe("");
});
