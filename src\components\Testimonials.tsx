import React, { ReactNode } from "react";

import {
  Avatar,
  Box,
  Container,
  Flex,
  HStack,
  <PERSON>ing,
  Stack,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import { TESTIMONIALS } from "pages/data";
import { slugify } from "utils/utils";
const wrapBrand = (
  array: string[],
  fn1: (str: string) => React.JSX.Element,
  fn2: (str: string) => React.JSX.Element,
) => {
  let fn = fn1;
  const output = [];
  for (let i = 0; i < array.length; i++) {
    output[i] = fn.call(this, array[i]);
    // toggle between the two functions
    fn = fn === fn1 ? fn2 : fn1;
  }
  return output;
};
// const toParts = (str: string) => str.split(/\bTenK Solutions\b/g);
const toParts = (str: string) => str.split(/[[\]]/g);

const Testimonial = ({ children }: { children: ReactNode }) => {
  return <Box minW={{ base: "85%", sm: 300 }}>{children}</Box>;
};

const TestimonialContent = ({ children }: { children: ReactNode }) => {
  return (
    <Stack
      bg={useColorModeValue("white", "gray.800")}
      boxShadow={"lg"}
      p={8}
      rounded={"xl"}
      align={"center"}
      pos={"relative"}
      _after={{
        content: `""`,
        w: 0,
        h: 0,
        borderLeft: "solid transparent",
        borderLeftWidth: 16,
        borderRight: "solid transparent",
        borderRightWidth: 16,
        borderTop: "solid",
        borderTopWidth: 16,
        borderTopColor: useColorModeValue("white", "gray.800"),
        pos: "absolute",
        bottom: "-16px",
        left: "50%",
        transform: "translateX(-50%)",
      }}
    >
      {children}
    </Stack>
  );
};

const TestimonialHeading = ({ children }: { children: ReactNode }) => {
  return (
    <Heading as={"h3"} fontSize={"xl"}>
      {children}
    </Heading>
  );
};

const TestimonialText = ({ children }: { children: string }) => {
  const chirren = wrapBrand(
    toParts(children),
    function (str: string) {
      return (
        <Text key={slugify(str).slice(0, 16)} as={"span"}>
          {str}
        </Text>
      );
    },
    function (str: string) {
      return (
        <Text
          key={Math.floor(Math.random() * 100)}
          as={"span"}
          color="colors.brand.primary.300"
        >
          {str}
        </Text>
      );
    },
  );
  return (
    <Text
      textAlign={"center"}
      color={useColorModeValue("gray.600", "gray.400")}
      fontSize={"sm"}
    >
      {chirren}
    </Text>
  );
};

const TestimonialAvatar = ({
  src,
  name,
  title,
}: {
  src: string;
  name: string;
  title: string;
}) => {
  return (
    <Flex align={"center"} mt={8} direction={"column"}>
      <Avatar src={src} mb={2} />
      <Stack spacing={-1} align={"center"}>
        <Text fontWeight={600}>{name}</Text>
        <Text fontSize={"sm"} color={useColorModeValue("gray.600", "gray.400")}>
          {title}
        </Text>
      </Stack>
    </Flex>
  );
};

export default function WithSpeechBubbles() {
  return (
    <Box bg={useColorModeValue("gray.100", "gray.700")} py={16}>
      <Container maxW={"full"} px={{ base: 2 }}>
        <Stack spacing={0} align={"center"}>
          <Heading>Our Clients Speak</Heading>
          <Text>We have been working with clients around the country</Text>
        </Stack>
        <HStack mt={8} overflowX="scroll" spacing={8}>
          {TESTIMONIALS.map(([image, headline, text, name, company], i) => {
            return (
              <Testimonial key={i}>
                <TestimonialContent>
                  <TestimonialHeading>{headline}</TestimonialHeading>
                  <TestimonialText>{text}</TestimonialText>
                </TestimonialContent>
                <TestimonialAvatar src={image} name={name} title={company} />
              </Testimonial>
            );
          })}
        </HStack>
      </Container>
    </Box>
  );
}
