# TenK Solutions Copilot Instructions

## Project Overview

TenK Solutions is a React/TypeScript web application using Vite and Vike for SSR/SSG. The project follows a component-based architecture with Chakra UI for styling and accessibility.

## Key Architecture Patterns

### Page Structure

- Pages live in `src/pages/` using the `+Page.tsx` naming convention
- Each page corresponds to a route and typically imports components from `src/sections/`
- Example: `src/pages/articles/+Page.tsx` imports `Articles` from `src/sections/Articles.tsx`

### Component Organization

- **Components (`src/components/`)**: Reusable UI components like `QuestionCard`, `Navigation`
- **Sections (`src/sections/`)**: Page-specific larger components combining multiple base components
- **Layout Components**: `PageShell.tsx` in `renderer/` handles the app shell

### Styling Patterns

- Uses Chakra UI with custom theme in `src/utils/themes.ts`
- Responsive design using <PERSON><PERSON>'s responsive object syntax:

```tsx
fontSize={{ base: "2xl", md: "3xl" }}
px={{ base: 2, md: 10 }}
```

### State Management & Data Flow

- Page data defined in `src/pages/data.ts`
- Components use React hooks for local state
- Example pattern from `QuestionsAndAnswers.tsx`:

```tsx
const cardsPerView = useBreakpointValue({ base: 1, md: 2 }) || 1;
const [currentSlide, setCurrentSlide] = useState(0);
```

## Development Workflow

### Key Commands

```bash
yarn dev        # Development with hot reload
yarn prod       # Production build + server
yarn test       # Run Vitest tests
yarn pretty    # Format code with Prettier
```

### Server-Side Rendering

- Entry point: `src/server/index.ts`
- Page context handled by `renderer/+config.h.ts`
- Client rendering in `renderer/+onRenderClient.tsx`

## Testing Patterns

- Test files live next to implementation: `src/tests/`
- Use React Testing Library patterns
- Example: `contact_form.test.tsx` for form validation tests

## Common Patterns

### Carousel/Slider Components

- Use consistent height over width (`minH`/`maxH`)
- Include padding for shadow visibility
- Handle touch/mouse events for interactivity
  Example from `QuestionsAndAnswers.tsx`:

```tsx
<Box
  ref={carouselRef}
  overflow="hidden"
  px={{ base: 2, md: 8 }}
  cursor={slidesCount > cardsPerView ? "grab" : "default"}
>
```

### Form Components

- Use Chakra UI form components
- Include proper validation and error states
- Example: `src/components/ContactForm.tsx`

## External Integrations

- Google Analytics via `react-ga`
- Calendly scheduling via `react-calendly`
- Netlify for deployment (see badge in README)
