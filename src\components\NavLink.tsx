"use client";

import { slugify } from "#utils/utils";
import { Button, useColorModeValue } from "@chakra-ui/react";
import React, { ReactNode } from "react";

export const Links = [
  /**
   * Adding/removing also adds a kebab-case link in routes
   */
  "SERVICES",
  "CONTACT US",
  "CASE STUDIES",
  "ABOUT US",
  "PRICING",
  // "ARTICLES",
  // "PRIVACY",
];
export const HomeLink = () => {
  const linkTextColor = useColorModeValue("gray.200", "gray.700");
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (window.location.pathname !== "/") {
      window.history.pushState({}, "", "/");
      window.dispatchEvent(new PopStateEvent("popstate"));
    }
  };
  return (
    <Button
      w={"full"}
      variant={{ base: "unstyled", sm: "ghost" }}
      title="TenK Solutions Home Page"
      onClick={handleClick}
      _hover={{
        textDecoration: "none",
        bg: linkTextColor,
      }}
    >
      HOME
    </Button>
  );
};
export const NavLink = ({
  onClose,
  children: link,
  path,
}: {
  onClose?: () => void;
  children: ReactNode;
  path: string;
}) => {
  const linkName = `/${slugify(link as string)}`;
  const isCurrentPage = slugify(link as string) === path;
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (window.location.pathname !== linkName + "/") {
      window.history.pushState({}, "", linkName + "/");
      window.dispatchEvent(new PopStateEvent("popstate"));
    }
    if (onClose) onClose();
  };
  return (
    <Button
      w={"full"}
      variant={{ base: "unstyled", sm: "ghost" }}
      colorScheme="colors.brand.primary"
      textAlign={"center"}
      onClick={handleClick}
      title={`Navigate to ${linkName}`}
      isDisabled={isCurrentPage}
    >
      {link}
    </Button>
  );
};
