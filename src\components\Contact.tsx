import { CONTACT_ITEMS } from "#pages/data";
import {
  Box,
  Button,
  Container,
  Divider,
  HStack,
  Heading,
  Icon,
  IconButton,
  Link,
  VStack,
  Wrap,
  WrapItem,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";
import { IconType } from "react-icons";
import {
  BsFacebook,
  BsInstagram,
  BsLinkedin,
  BsTwitter,
} from "react-icons/bs/index.js";
import ContactForm from "./ContactForm";

const Contact = () => {
  const heading_text = useColorModeValue(
    "colors.brand.accent.700",
    "colors.brand.accent.200"
  );
  const bgColor = useColorModeValue("gray.50", "colors.brand.primary.900");
  const wrapBgColor = useColorModeValue("colors.brand.primary.50", "colors.brand.primary.1000");
  const icons = useColorModeValue(
    "colors.brand.primary.700",
    "colors.brand.primary.200"
  );
  return (
    <Container bg={bgColor} maxW="full" centerContent id="Contact">
      <Box p={8}>
        <Heading textAlign={"center"} fontSize={"4xl"} as="h1">
          Let's get started
        </Heading>
        <Box mt={{ sm: 3, md: 3, lg: 5 }} color={heading_text} px={3}>
          <Heading fontSize={"3xl"} as="h2">
            Schedule a discovery call today and let's get to solving!
          </Heading>
        </Box>
        <Divider />
        <VStack
          py={{ base: 0, sm: 5, md: 8, lg: 10 }}
          pl={0}
          spacing={{ base: 0, sm: 3 }}
          alignItems="flex-start"
        >
          <Box>
            <Heading textAlign="center" mb={5} fontSize={"2xl"} as="h3">
              Contact Us
            </Heading>

            <Wrap>
              <WrapItem
                bg={wrapBgColor}
                borderRadius={"md"}
                width={{ base: "full", lg: "auto" }}
              >
                <VStack
                  py={{ base: 0, sm: 5, md: 8, lg: 10 }}
                  pl={0}
                  justify={"space-between"}
                  // spacing={{ base: 0, sm: 10 }}
                  alignItems="flex-start"
                  height="100%"
                >
                  {CONTACT_ITEMS.map(({ icon, data, hrefPrefix }, i) => (
                    <Link
                      href={`${hrefPrefix}${data}`}
                      key={i}
                      isExternal
                      title="Contact TenK Solutions"
                    >
                      <Button
                        fontSize={"lg"}
                        width={{ base: "100%", xs: "250px" }}
                        variant="links"
                        userSelect={"text"}
                        // _hover={{ border: "2px solid colors.brand.primary.400" }}
                        leftIcon={<Icon boxSize={7} as={icon} color={icons} />}
                        title="Contact TenK Solutions"
                      >
                        <Heading as="h4" fontSize="lg">
                          {data}
                        </Heading>
                      </Button>
                    </Link>
                  ))}
                </VStack>
              </WrapItem>
              <WrapItem width={{ base: "full", lg: "auto" }}>
                <ContactForm />
              </WrapItem>
            </Wrap>
            <HStack
              mt={{ lg: 10, md: 10 }}
              spacing={{ base: "auto", sm: 5 }}
              px={10}
              justifyContent={"space-between"}
              alignItems="flex-start"
              bg={wrapBgColor}
              borderRadius={"md"}
            >
              <SocialButton
                domain={"linkedin"}
                handle={"tenksolutions"}
                Icon={BsLinkedin}
              />
              <SocialButton
                domain={"twitter"}
                handle={"tenk_solutions"}
                Icon={BsTwitter}
              />
              <SocialButton
                domain={"facebook"}
                handle={"tenksolutionsllc"}
                Icon={BsFacebook}
              />
              <SocialButton
                domain={"instagram"}
                handle={"tenksolutions"}
                Icon={BsInstagram}
              />
            </HStack>
          </Box>
        </VStack>
      </Box>
    </Container>
  );
};

export default Contact;

function SocialButton({
  domain,
  handle,
  Icon,
}: {
  domain: string;
  handle: string;
  Icon: IconType;
}) {
  return (
    <Link
      href={`https://www.${domain}.com/${
        domain === "linkedin" ? "company/" : ""
      }${handle}`}
      isExternal
      title={`TenK Solutions ${domain} Page`}
    >
      <IconButton
        aria-label={`TenK Solutions ${domain} Page`}
        title={`TenK Solutions ${domain} Page`}
        variant="ghost"
        size="lg"
        isRound={true}
        // _hover={{ bg: "#eee" }}
        colorScheme={"colors.brand.primary"}
        icon={<Icon size="28px" />}
      />
    </Link>
  );
}
