export const themeByAI = {
  colors: {
    brand: {
      primary: {
        50: "#EEE9FF", // Lightest - for backgrounds/highlights
        100: "#D1C7FF",
        200: "#B3A5FF",
        300: "#9683FF",
        400: "#7961FF",
        500: "#381CC9", // Original brand color
        600: "#2D16A1", // Darker for hover states
        700: "#221179",
        800: "#160B52",
        900: "#0B062A", // Darkest - for text/shadows
      },
      // Secondary Red (#81052C) scale
      secondary: {
        50: "#FFE5EC", // Lightest
        100: "#FFB8C7",
        200: "#FF8AA3",
        300: "#FF5C7E",
        400: "#FF2E59",
        500: "#81052C", // Original brand color
        600: "#670423",
        700: "#4D031A",
        800: "#330211",
        900: "#1A0109", // Darkest
      },
    },
  },
  fonts: {
    heading: "Exo 2, sans-serif",
    body: "Inter, system-ui, sans-serif",
  },
  components: {
    Container: {
      baseStyle: {
        maxW: "container.xl",
        px: { base: 4, md: 8 },
      },
    },
    Button: {
      baseStyle: {
        fontFamily: '"Exo 2", sans-serif',
        fontWeight: "800",
        borderRadius: "lg",
      },
      variants: {
        solid: {
          bgGradient:
            "linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)",
          color: "white",
          _hover: {
            bg: "colors.brand.primary.600",
            transform: "translateY(-2px)",
            boxShadow: "lg",
          },
          transition: "all 0.2s",
        },
        secondary: {
          bg: "colors.brand.secondary.500",
          color: "white",
          _hover: {
            bg: "colors.brand.secondary.600",
            transform: "translateY(-2px)",
            boxShadow: "lg",
          },
        },
        outline: {
          borderColor: "colors.brand.primary.500",
          color: "colors.brand.primary.500",
          _hover: {
            bg: "colors.brand.primary.50",
          },
        },
        ghost: {
          color: "colors.brand.primary.500",
          _hover: {
            bg: "colors.brand.primary.50",
          },
          _dark: {
            color: "colors.brand.primary.100",
            _hover: {
              bg: "colors.brand.primary.800",
            },
          },
        },
      },
      sizes: {
        lg: {
          fontSize: "md",
          px: 8,
          h: 14,
        },
      },
    },
    Card: {
      baseStyle: {
        container: {
          bg: "white",
          borderRadius: "xl",
          boxShadow: "lg",
          _hover: {
            transform: "translateY(-4px)",
            boxShadow: "xl",
          },
        },
      },
    },
    Heading: {
      baseStyle: {
        bgGradient:
          "linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)",
        bgClip: "text",
        fontFamily: '"Exo 2", sans-serif',
        fontWeight: 800,
        _dark: {
          bgGradient:
            "linear(to-r, colors.brand.secondary.100, colors.brand.primary.100)",
        },
      },
    },
    Input: {
      variants: {
        outline: {
          field: {
            borderColor: "gray.400", // Light mode
            _hover: {
              borderColor: "colors.brand.primary.400",
            },
            _focus: {
              borderColor: "colors.brand.primary.500",
            },
            boxShadow: "0 0 0 1px #381CC9",
            background: "gray.100",
            _dark: {
              borderColor: "gray.500", // Dark mode
              background: "gray.900",
              _hover: {
                borderColor: "colors.brand.primary.200",
              },
              _focus: {
                borderColor: "colors.brand.primary.200",
              },
            },
          },
        },
      },
    },
    Select: {
      variants: {
        outline: {
          field: {
            borderColor: "gray.400",
            _hover: {
              borderColor: "colors.brand.primary.400",
            },
            _focus: {
              borderColor: "colors.brand.primary.500",
            },
            boxShadow: "0 0 0 1px #381CC9",
            background: "gray.100",
            _dark: {
              borderColor: "gray.500",
              _hover: {
                borderColor: "colors.brand.primary.200",
              },
              _focus: {
                borderColor: "colors.brand.primary.200",
              },
              background: "gray.900",
            },
          },
        },
      },
    },
  },
  // styles: {
  //   global: {
  //     body: {
  //       bg: 'white',
  //       color: 'gray.800',
  //     }
  //   }
  // }
};

export const themeByComponents = {
  components: {
    Button: {
      defaultProps: {
        // colorScheme: "colors.brand.secondary", // default is gray
      },
    },
  },
};
// Common gradients using the scale
export const brandGradients = {
  primary: "linear(to-r, colors.brand.primary.500, colors.brand.primary.400)",
  secondary:
    "linear(to-r, colors.brand.secondary.500, colors.brand.secondary.400)",
  hero: "linear(to-r, colors.brand.primary.500 15%, colors.brand.secondary.500 85%)",
  subtle: "linear(to-r, colors.brand.primary.50, colors.brand.secondary.50)",
  card: "linear(to-b, white, colors.brand.primary.50)",
};
