import { IconButton } from "@chakra-ui/react";
import { motion, useScroll, useTransform } from "framer-motion";
import React from "react";
import { FaArrowUp } from "react-icons/fa/index.js";

const MotionButton = motion(IconButton);

export const ScrollToTop = () => {
  const { scrollYProgress } = useScroll();
  const opacity = useTransform(scrollYProgress, [0, 0.2], [0, 1]);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <MotionButton
      aria-label="Scroll to top"
      icon={<FaArrowUp />}
      size="lg"
      position="fixed"
      bottom="4"
      right="4"
      zIndex="tooltip"
      style={{ opacity }}
      onClick={scrollToTop}
      bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
      color="white"
      _hover={{
        transform: "translateY(-2px)",
      }}
    />
  );
};