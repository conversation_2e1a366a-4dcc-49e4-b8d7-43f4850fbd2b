import { CurvedDivider } from "#components/CurvedDivider";
import { ParallaxSection } from "#components/ParallaxSection";
import { HeroSection } from "#sections/HeroSection";
import IQAAssessments from "#sections/IQAAssessments";
import PartnersSection from "#sections/Partners";
import QuestionnaireSlider from "#sections/QuestionsAndAnswers";
import WhatWeDo from "#sections/WhatWeDo";
import React from "react";

function HomePage() {
  return (
    <>
      <HeroSection />
      <CurvedDivider flip />

      <ParallaxSection offsetY={20}>
        <WhatWeDo />
      </ParallaxSection>

      <CurvedDivider />

      <ParallaxSection offsetY={30}>
        <QuestionnaireSlider />
      </ParallaxSection>

      <CurvedDivider flip />

      {/* <ParallaxSection offsetY={40}>
        <Features />
      </ParallaxSection>

      <CurvedDivider /> */}

      <ParallaxSection offsetY={20}>
        <IQAAssessments />
      </ParallaxSection>

      <CurvedDivider />

      <ParallaxSection offsetY={20}>
        <PartnersSection />
      </ParallaxSection>
    </>
  );
}

export default HomePage;
