import {
  Box,
  Heading,
  Image,
  ListItem,
  Text,
  UnorderedList,
} from "@chakra-ui/react";
import * as React from "react";
import Capabilities from "./Capabilities";

// Data
const serviceOfferings = [
  {
    title: "Unified Communications (UCaaS)",
    description:
      "Replace traditional phone systems with feature-rich, scalable UCaaS platforms for enterprise collaboration.",
  },
  {
    title: "Contact Center (CCaaS)",
    description:
      "Deliver superior customer experiences with omnichannel CCaaS solutions enabling efficient routing and global reach.",
  },
  {
    title: "Cybersecurity",
    description:
      "Develop robust security frameworks to prevent data breaches and malicious threats in today's ever-evolving landscape.",
  },
  {
    title: "Connectivity",
    description:
      "Connect globally with the right internet, network, wireless, IoT, and mobility solutions to enable efficiency and scale.",
  },
  {
    title: "Cloud & Colocation",
    description:
      "Leverage cloud computing resources and data centers worldwide for flexible, high-performance IT infrastructure.",
  },
];

export default function AboutPage() {
  return (
    <Box pt={8} pb={12} px={8} margin={"auto"} maxW="3xl" textAlign={"justify"}>
      {/* <Heading as="h1" textAlign="center" p={6} mb={8}>
        About TenK Solutions
      </Heading> */}
      <Image
        src="/img/logo-no-background.webp"
        py={5}
        alt="TenK Solutions, LLC Large Logo"
      />
      {/* {ABOUT_SECTIONS.map((section) => (
        <Box key={section.title} mb={8}>
          <Heading as="h2" size="lg" mb={4} textAlign="center">
            {section.title}
          </Heading>

          {section.content.map((para, i) => (
            <Text textAlign="justify" mb={4} key={i}>
              {para.text}
            </Text>
          ))}
        </Box>
      ))} */}
      <Heading my={3} fontSize="xl" as={"h1"}>
        TenK Solutions, LLC is a leading systems integration consultancy
        providing expert IT strategies and solutions to drive business success.
      </Heading>
      <Text as="p" my={3} fontSize="lg">
        With over 15 years of experience spanning various industries, we
        understand the unique technology needs of professional services firms.
        Through close partnerships with over 200 top technology vendors, TenK
        Solutions designs and implements tailored solutions spanning:
        <UnorderedList py={3}>
          {serviceOfferings.map(({ title, description }) => (
            <ListItem pb={3} key={title}>
              <Heading as="h3" fontSize={"xl"} display={"inline"}>
                {title}
              </Heading>
              {" - "}
              {description}
            </ListItem>
          ))}
        </UnorderedList>
      </Text>
      <Text as="p" my={3} fontSize="lg">
        Our approach is consultative - we advocate for strategies that maximize
        investments, enable seamless collaboration, ensure data safety, and
        align technology with core business goals.
      </Text>
      {/**
      <VStack py={7} maxW="2xl" dir="column" margin="auto">
        <Heading as="h2" my={3}>
          Principal: Kiel Byrne
        </Heading>
        <Image src="/img/headshot.png" height="17em" borderRadius={"md"} />
        <Text my={3} align="left" fontSize="lg">
          TenK Solutions, LLC was founded by Kiel Byrne, a methodical systems
          engineer and IT consultant committed to customer service and
          innovation. Kiel's 15+ year career spans integrating and optimizing IT
          for top AEC firms like Perkins&Will, HOK, and CoStar Group. <br />
          This hands-on technical acumen combined with strong emotional
          intelligence allows him to successfully deliver customized,
          best-of-breed solutions as a trusted advisor.
        </Text>
      </VStack>
       */}
      <Capabilities />
    </Box>
  );
}
