import { FloatingCTA } from "#components/FloatingCTA";
import { PageTransition } from "#components/PageTransition";
import { ScrollProgressBar } from "#components/ScrollProgressBar";
import { ScrollProvider } from "#components/ScrollProvider";
import { ScrollToTop } from "#components/ScrollToTop";
import { AnimatePresence } from "framer-motion";
import React from "react";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ScrollProvider>
      <ScrollProgressBar />
      <AnimatePresence mode="wait">
        <PageTransition key="page">{children}</PageTransition>
      </AnimatePresence>
      <ScrollToTop />
      <FloatingCTA />
    </ScrollProvider>
  );
}
