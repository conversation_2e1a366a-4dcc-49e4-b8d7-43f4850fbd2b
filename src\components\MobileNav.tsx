import usePageContext from "#renderer/usePageContext";
import { isNotPrivacyString, normalizeURLPath } from "#utils/utils";
import { CloseIcon, HamburgerIcon } from "@chakra-ui/icons";
import {
  Box,
  HStack,
  IconButton,
  Image,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";

import {
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaTwitter,
} from "react-icons/fa/index.js";
import { HomeLink, Links, NavLink } from "./NavLink";
import { SocialButton } from "./SocialButton";

const MotionBox = motion(Box);

export const MobileNav = ({
  isOpen,
  onToggle,
}: {
  isOpen: boolean;
  onToggle: () => void;
}) => {
  const { urlPathname: path } = usePageContext();

  const menuVariants = {
    open: {
      x: 0,
      transition: { type: "spring", stiffness: 300, damping: 30 },
    },
    closed: {
      x: "100%",
      transition: { type: "spring", stiffness: 300, damping: 30 },
    },
  };

  const bgColor = useColorModeValue("white", "gray.800");
  return (
    <>
      <IconButton
        display={{ base: "flex", md: "none" }}
        onClick={onToggle}
        icon={isOpen ? <CloseIcon /> : <HamburgerIcon />}
        variant="ghost"
        aria-label="Toggle Navigation"
        position="fixed"
        top={4}
        right={4}
        zIndex="overlay"
      />

      <AnimatePresence>
        {isOpen && (
          <>
            <MotionBox
              position="fixed"
              top={0}
              left={0}
              right={0}
              bottom={0}
              bg="blackAlpha.600"
              zIndex="overlay"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={onToggle}
            />
            <MotionBox
              position="fixed"
              top={0}
              right={0}
              bottom={0}
              width="full"
              maxW="xs"
              bg={bgColor}
              boxShadow="2xl"
              zIndex="overlay"
              initial="closed"
              animate="open"
              exit="closed"
              variants={menuVariants}
            >
              <VStack spacing={4} align="stretch" pt={20} px={4}>
                <Image
                  height={10}
                  width={"auto"}
                  src={"/img/logo-no-background.webp"}
                  alt="TenK Solutions, LLC Large Logo"
                  objectFit="contain"
                />
                <HomeLink />
                {Links.map(
                  (link, i) =>
                    isNotPrivacyString(link) && (
                      <Box
                        width="full"
                        textAlign={"center"}
                        borderWidth={"thin"}
                        key={i}
                      >
                        <NavLink
                          onClose={onToggle}
                          path={normalizeURLPath(path)}
                        >
                          {link}
                        </NavLink>
                      </Box>
                    )
                )}
                <HStack spacing={6} justifyContent={"center"}>
                  <SocialButton
                    label={"LinkedIn"}
                    href={"https://www.linkedin.com/company/tenksolutions"}
                  >
                    <FaLinkedin />
                  </SocialButton>
                  <SocialButton
                    label={"Instagram"}
                    href={"https://www.instagram.com/tenksolutions"}
                  >
                    <FaInstagram />
                  </SocialButton>
                  <SocialButton
                    label={"Facebook"}
                    href={"https://www.facebook.com/tenksolutionsllc"}
                  >
                    <FaFacebook />
                  </SocialButton>
                  <SocialButton
                    label={"X"}
                    href={"https://www.twitter.com/tenk_solutions"}
                  >
                    <FaTwitter />
                  </SocialButton>
                </HStack>
              </VStack>
            </MotionBox>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
