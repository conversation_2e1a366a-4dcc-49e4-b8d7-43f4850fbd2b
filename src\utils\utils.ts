import RGA from "react-ga";

export const slugify = (str: string) =>
  str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "");

export function toTitleCase(str: string) {
  return str.replace(/\w\S*/g, function (txt) {
    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
  });
}

export const isNotPrivacyString = (link: string) => link !== "Privacy";

export const useAnalyticsEventTracker = (category = "Lead Generation") => {
  const eventTracker = (action = "new_calendly", label = "new appointent") => {
    RGA.event({ category, action, label });
  };
  return eventTracker;
};

export const normalizeURLPath = (path: string) => {
  return path
    .split("/")
    .filter((segment) => segment !== "")
    .join("/")
    .toLowerCase();
}

export const titleize = (slug: string) => {
  if (slug.includes("-")) {
    var words = slug.split("-");
    return words
      .map(function (word) {
        return word.charAt(0).toUpperCase() + word.substring(1).toLowerCase();
      })
      .join(" ");
  } else return toTitleCase(slug);
};

export const setDirectionByNum = (number: number) => {
  if (number % 2 === 0) {
    return "left";
  } else {
    return "right";
  }
};
export const setOffsetByNum = (number: number) => {
  if (number % 2 === 0) {
    return "-300px";
  } else {
    return "300px";
  }
};

