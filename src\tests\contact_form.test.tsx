// ContactForm.test.tsx
import { ContactForm } from "#components/ContactForm";
import { FormOptions } from "#pages/data";
import { ChakraProvider } from "@chakra-ui/react";
import { fireEvent, render, screen } from "@testing-library/react";
import * as React from "react";
import { describe, expect, it } from "vitest";

// Utility to render with Chakra UI provider
const renderWithChakraProvider = (ui: React.ReactNode) => {
  render(<ChakraProvider>{ui}</ChakraProvider>);
};

describe("ContactForm component", () => {
  it("renders form inputs and elements correctly", () => {
    renderWithChakraProvider(<ContactForm />);

    // Check for input fields
    expect(screen.getByLabelText("Your Name")).toBeDefined();
    expect(screen.getByLabelText("Your Best Contact Email")).toBeDefined();
    expect(screen.getByLabelText("Your Company Name")).toBeDefined();
    expect(screen.getByLabelText("Your message")).toBeDefined();

    // Check for dropdown and radio button groups
    expect(screen.getByLabelText("Primary Service Needs")).toBeDefined();
    expect(screen.getByText("Urgency Level")).toBeDefined();

    // Check for submit button
    expect(screen.getByRole("button", { name: "Send message" })).toBeDefined();
  });

  it("displays form options correctly in select dropdowns and radio buttons", () => {
    renderWithChakraProvider(<ContactForm />);

    // Open the select dropdown and check for service options
    fireEvent.mouseDown(screen.getByLabelText("Primary Service Needs"));
    FormOptions.services.forEach((service) => {
      expect(screen.getByText(service)).toBeDefined();
    });

    // Check radio options for urgency levels
    FormOptions.urgencyLevels.forEach((urgency) => {
      expect(screen.getByLabelText(urgency)).toBeDefined();
    });
  });

  it("shows validation errors if required fields are not filled out on submit", () => {
    renderWithChakraProvider(<ContactForm />);
    //@ts-expect-error
    const submitButton = screen.getByRole("button", { value: "Send Message" });
    fireEvent.click(submitButton);

    // Check for validation errors
    // expect(screen.getByText("Your Name is required")).toBeDefined();
    // expect(screen.getByText("Please fill out this field.")).toBeDefined();
    // expect(screen.getByText("Primary Service Needs is required")).toBeDefined();
    // expect(screen.getByText("Urgency Level is required")).toBeDefined();
  });
});
