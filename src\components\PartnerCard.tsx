import { Box, Flex, Link, Text, useColorModeValue } from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";

const MotionFlex = motion(Flex);

export const PartnerCard = ({
  name,
  logo,
  website,
}: {
  name?: string;
  logo: string;
  website: string;
}) => {
  const bgColor = useColorModeValue("white", "gray.800");
  const blendMode = useColorModeValue("luminosity", "screen");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const partnerBgColor = useColorModeValue("whiteAlpha.900", "blackAlpha.900")
  return (
    <Link 
      href={website}
      isExternal
      _hover={{ textDecoration: "none" }}
      title={name}
    >
      <MotionFlex
        direction="column"
        align="center"
        justify="center"
        p={6}
        borderRadius="xl"
        borderWidth="1px"
        borderColor={borderColor}
        bg={bgColor}
        position="relative"
        overflow="hidden"
        whileHover={{ y: -8, boxShadow: "xl" }}
        transition={{ duration: 0.3 }}
      >
        <Box
          backgroundImage={`url(${logo})`}
          w="full"
          h="80px"
          backgroundSize="contain"
          backgroundPosition="center"
          backgroundRepeat="no-repeat"
          transition="all 0.4s ease-in-out"
          opacity={0.9}
          mixBlendMode={blendMode}
          _groupHover={{ opacity: 1, transform: "scale(1.05)" }}
        />
        {name && (
          <Text
            position="absolute"
            bottom={0}
            left={0}
            right={0}
            textAlign="center"
            py={2}
            bg={partnerBgColor}
            fontSize="sm"
            fontWeight="medium"
            opacity={0}
            transform="translateY(100%)"
            transition="all 0.3s"
            _groupHover={{ opacity: 1, transform: "translateY(0)" }}
          >
            {name}
          </Text>
        )}
      </MotionFlex>
    </Link>
  );
};
