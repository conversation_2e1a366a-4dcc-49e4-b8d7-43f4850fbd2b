import { Box, BoxProps } from "@chakra-ui/react";
import { motion, useScroll, useTransform } from "framer-motion";
import React, { useRef } from "react";

const MotionBox = motion(Box);

interface ParallaxSectionProps extends BoxProps {
  children: React.ReactNode;
  offsetY?: number;
}

export const ParallaxSection = ({
  children,
  offsetY = 50,
  ...rest
}: ParallaxSectionProps) => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
  });

  const y = useTransform(scrollYProgress, [0, 1], [offsetY, -offsetY]);

  return (
    <MotionBox
      ref={ref}
      style={{ y }}
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.3 }}
      {...rest}
    >
      {children}
    </MotionBox>
  );
};
