import {
  Box,
  Button,
  Fade,
  Heading,
  Icon,
  Link,
  SimpleGrid,
  useColorModeValue,
  useTheme,
} from "@chakra-ui/react";
import * as React from "react";
import { FaPhoneAlt, FaQuestionCircle } from "react-icons/fa/index.js";

// FAQ data array

const faqs = [
  {
    question:
      "How can I use technology to help my small business reach the next growth milestone?",
    answer:
      "Our IT experts can provide customized solutions like workflow automation, data analytics, and custom software to help your small business hit growth goals and milestones. We partner with you to understand your needs and deploy the right technology.",
  },
  {
    question:
      "What IT services can help me set up a new retail location or office?",
    answer:
      "We install and configure networks, WiFi, POS systems, VoIP phones, security cameras, and any other required business technology.",
  },
  {
    question: "What types of custom software can improve my workflows?",
    answer:
      "Our expert developers can build customized software and web apps tailored to optimize your unique business workflows. From inventory or order management to HR systems, we can improve efficiency with solutions designed specifically for your processes.",
  },
  {
    question:
      "How can I optimize my existing IT infrastructure to improve performance?",
    answer:
      "We provide full managed IT services to help optimize your technology stack. This includes monitoring, updates, troubleshooting, security, help desk support, and more. We'll make sure your infrastructure aligns to business goals.",
  },
  {
    question:
      "How can automating processes improve my efficiency and cut costs?",
    answer:
      "By automating repetitive tasks, our solutions can significantly cut costs while improving quality and efficiency. We identify automation opportunities and implement the right technologies like scripting, RPA, APIs, and more to optimize your processes.",
  },

  {
    question: "How can you refresh my existing website design?",
    answer:
      "Our expert web developers can overhaul your current website with a completely new modern design. We'll carry over and optimize your content while giving your site a visual makeover with the latest design trends and features.",
  },
];

const Faq = () => {
  const cardBg = useColorModeValue("whiteAlpha.80", "blackAlpha.700");
  const cardBorder = useColorModeValue("whiteAlpha.400", "whiteAlpha.200");
  const cardText = useColorModeValue("gray.700", "gray.200");
  const theme = useTheme();

  return (
    <Box px={{ base: 2, md: 8 }} py={12} position="relative" zIndex={1}>
      {/* Animated gradient background */}
      <Box
        position="absolute"
        inset={0}
        zIndex={0}
        bgGradient="linear(120deg, rgba(0,120,255,0.10) 0%, rgba(120,0,255,0.10) 100%)"
        backgroundSize="200% 200%"
        animation={theme.animations?.gradientMove || "none"}
        filter="blur(12px)"
        pointerEvents="none"
      />
      <Heading
        as="h1"
        textAlign="center"
        mb={6}
        fontSize={{ base: "2xl", md: "3xl" }}
        fontWeight="extrabold"
        letterSpacing="-0.02em"
      >
        Frequently Asked Questions
      </Heading>
      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} zIndex={1}>
        {faqs.map((faq, i) => (
          <Fade in={true} key={i} style={{ transitionDelay: `${i * 80}ms` }}>
            <Box
              bg={cardBg}
              borderRadius="2xl"
              boxShadow="2xl"
              border="1.5px solid"
              borderColor={cardBorder}
              p={6}
              display="flex"
              flexDirection="column"
              gap={2}
              backdropFilter="auto"
              backdropBlur="18px"
              color={cardText}
              minH="120px"
            >
              <Box display="flex" alignItems="center" gap={3} mb={1}>
                <Icon as={FaQuestionCircle} color="blue.400" boxSize={6} />
                <Heading as="h2" fontSize="lg" fontWeight="bold" flex={1}>
                  {faq.question}
                </Heading>
              </Box>
              <Box fontSize="md" color={cardText}>
                {faq.answer}
              </Box>
            </Box>
          </Fade>
        ))}
      </SimpleGrid>
      <Box textAlign="center" mt={10} zIndex={1}>
        <Button
          as={Link}
          href="tel:+15551234567"
          leftIcon={<FaPhoneAlt />}
          colorScheme="blue"
          size="lg"
          fontWeight="bold"
          px={8}
          py={6}
          borderRadius="xl"
          boxShadow="lg"
        >
          Still have questions? Call for instant answers
        </Button>
        <Button as={Link} href="/contact-us" variant="ghost" size="lg" ml={4}>
          Or request a quote
        </Button>
      </Box>
    </Box>
  );
};

export default Faq;

/**
 * Managed IT services for SMBs
Technology solutions for restaurants
Retail tech solutions
Custom software development for SMEs
IT support for emerging businesses
Automation solutions for small business
 */
