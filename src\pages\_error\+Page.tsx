// Import necessary dependencies
import {
  Fade,
  Flex,
  Heading,
  Image,
  Link,
  SlideFade,
  Text,
} from "@chakra-ui/react";
import React from "react";
import usePageContext from "../../renderer/usePageContext";

const NotFoundPage: React.FC = () => {
  const pageContext = usePageContext();

  let msg: string; // Message shown to the user
  const { abortReason, abortStatusCode } = pageContext;
  // if (abortReason?.notAdmin) {
  //   // Handle `throw render(403, { notAdmin: true })`
  //   msg = "You cannot access this page because you aren't an administrator."
  // } else
  if (typeof abortReason === "string") {
    // Handle `throw render(abortStatusCode, `You cannot access ${someCustomMessage}`)`
    msg = abortReason;
  } else if (abortStatusCode === 403) {
    // Handle `throw render(403)`
    msg =
      "You cannot access this page because you don't have enough privileges.";
  } else if (abortStatusCode === 401) {
    // Handle `throw render(401)`
    msg =
      "You cannot access this page because you aren't logged in. Please log in.";
  } else {
    // Fallback error message
    msg = pageContext.is404
      ? "The page you are looking for might be in another dimension."
      : "Something went wrong. Sincere apologies. Try again (later).";
  }

  return (
    <Flex
      align="center"
      justify="center"
      backgroundColor={"black"}
      direction="column"
      textAlign={"center"}
      paddingBlock={10}
    >
      {/* 404 Image */}
      <SlideFade in={true} reverse>
        <Image src="/img/404.webp" alt="404 Image" width="full" />
      </SlideFade>

      {/* 404 Content */}
      <Fade in={true}>
        <Heading as={"h1"} size="2xl" color="#2393ABF" mt={4}>
          Nope! We didn't build that page...
        </Heading>
        <Text fontSize="lg" color="#ea8922" mt={4}>
          {msg}
        </Text>
        <Text fontSize="lg" color="#ea8922" mt={4}>
          Let's go{" "}
          <Link href="/" title="TenK Solutions Home Page">
            back home
          </Link>
        </Text>
      </Fade>
    </Flex>
  );
};

export default NotFoundPage;
