import { Box, Button, ButtonProps } from "@chakra-ui/react";
import { motion } from "framer-motion";
import React, { useState } from "react";

const MotionBox = motion(Box);

export const ButtonWithRipple = ({ children, ...props }: ButtonProps) => {
  const [isRippling, setIsRippling] = useState(false);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    setIsRippling(true);
    setTimeout(() => setIsRippling(false), 500);
    props.onClick?.(e);
  };

  return (
    <Button
      position="relative"
      overflow="hidden"
      onClick={handleClick}
      {...props}
    >
      {children}
      {isRippling && (
        <MotionBox
          position="absolute"
          initial={{ scale: 0, opacity: 0.35 }}
          animate={{ scale: 2, opacity: 0 }}
          transition={{ duration: 0.5 }}
          style={{
            width: "100%",
            height: "100%",
            background: "white",
            borderRadius: "50%",
            pointerEvents: "none"
          }}
        />
      )}
    </Button>
  );
};