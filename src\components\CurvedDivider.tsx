import { Box, useColorModeValue } from "@chakra-ui/react";
import React from "react";

interface CurvedDividerProps {
  flip?: boolean;
  color?: string;
}

export const CurvedDivider = ({ flip = false }: CurvedDividerProps) => {
  const bgColor = useColorModeValue(
    "colors.brand.primary.900",
    "colors.brand.primary.100"
  );

  return (
    <Box
      height="150px"
      overflow="hidden"
      transform={flip ? "rotate(180deg)" : undefined}
      sx={{
        svg: {
          // Target the SVG element within the Box
          height: "100%",
          width: "100%",
          stroke: "none",
        },
        path: {
          // Target the path element within the SVG
          fill: bgColor,
        },
      }}
    >
      <svg viewBox="0 0 500 150" preserveAspectRatio="none">
        <path d="M0.00,49.98 C150.00,150.00 349.20,-50.00 500.00,49.98 L500.00,150.00 L0.00,150.00 Z" />
      </svg>
    </Box>
  );
};
