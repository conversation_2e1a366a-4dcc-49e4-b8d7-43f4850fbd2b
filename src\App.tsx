import React from "react";
import { PageContext } from "vike/types";
import { PageContextProvider } from "./renderer/PageContextProvider";

function App({
  children,
  pageContext,
}: {
  children: React.ReactNode;
  pageContext: PageContext;
}) {
  return (
    <PageContextProvider pageContext={pageContext}>
      {children}
    </PageContextProvider>
  );
}
export default App;

/**
 Hero section (featuring a catchy headline that highlights the IT consultant's unique value proposition, along with an engaging image or video that conveys the fun and charming personality)
Features section (highlighting the key services or expertise of the IT consultant in a visually appealing and engaging way)
Testimonials section (featuring testimonials or reviews from satisfied clients that highlight the IT consultant's knowledge, skills, and diverse capabilities)
Product Details section (providing more in-depth information about the IT consultant's services or solutions, showcasing the diversity of skills and expertise)
Contact form (allowing visitors to get in touch with the IT consultant easily, with a friendly and approachable tone)
Pricing section (detailing different pricing options or packages in a clear and transparent manner, showcasing the affordability and value of the services)
Footer (including contact information, social media buttons, and other relevant links to further engage and connect with the audience)
 */
