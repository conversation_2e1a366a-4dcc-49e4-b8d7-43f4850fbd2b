
[![Netlify Status](https://api.netlify.com/api/v1/badges/cb8d8fe6-dd59-4ed7-abcb-30e62d37a7c5/deploy-status)](https://app.netlify.com/sites/tenksolutions/deploys)

# [TenK Solutions, LLC](https://tenksolutions.com)
A small-business web application for **TenK Solutions, LLC**; an IT Consultant and Independent Contractor based in the Metro DMV Area.

## TenK Solutions Technology Stack:

### Frontend
- **React** - Popular JavaScript framework used for building out component-driven UI
- **TypeScript** - Language providing static typing for application logic
- **Vite** - Utilizes Vite dev server and build process for faster development, along with plugins liks Vike & Vitepress
- **Chakra UI** - Implements component library for accessible UI patterns
- **Framer Motion** - Animation library enabling complex UI animations
- **Emotion** - Enables CSS-in-JS styling for components
- **React Helmet** - Manages dynamic document head tags for SEO optimizations
- **React Icons** - Implements icon components in UI
- **React Markdown** - Renders markdown content to React components for easier blog development

### Backend
- **Express** - Fast and minimalist Node.js framework running on server
- **Node.js** - Asynchronous event-driven JavaScript runtime for building scalable network apps

### Tooling
- **ESLint** - Linting utility ensuring code quality and best practices
- **Prettier** - Automatic code formatting for consistency
- **Vite Plugins** - Leverages plugins like vike (SSR/SSG) and vite-plugin-react for extensibility

### Infrastructure
- **Netlify** - Hosting provider with optimized deployment from Git

### Methodologies
- **Responsive Design** - Implements mobile-first and fully responsive UI patterns
- **Server-side Generation** - Renders React app content on server for SEO and performance
- **Modular Components** - Breaks UI into reusable component building blocks
- **Git-based Workflow** - Uses Git for version control and Netlify deployments