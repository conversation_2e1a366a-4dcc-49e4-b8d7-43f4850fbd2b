"use client";

import { FormOptions } from "#pages/data";
// ContactForm.tsx
import {
  Button,
  FormControl,
  FormHelperText,
  FormLabel,
  HStack,
  Input,
  InputGroup,
  InputLeftElement,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Textarea,
  VStack,
} from "@chakra-ui/react";
import * as React from "react";
import { BsPerson } from "react-icons/bs/index.js";
import { MdOutlineEmail } from "react-icons/md/index.js";

// interface FormData {
//   name: string;
//   email: string;
//   message: string;
// }


export const ContactForm: React.FC = () => {
  return (
    <VStack
      spacing={6}
      width="100%"
      maxW="800px"
      mx="auto"
      borderRadius={"md"}
      borderWidth={"2px"}
      p={3}
      border="colors.brand.primary.50"
      dropShadow={"md"}
    >
      <form
        name="contact_form"
        method="POST"
        data-netlify="true"
        netlify-honeypot="bot-field"
        // action="/pages/success/" //netlify not allowing me to redirect properly; need further investigaion
      >
        <VStack spacing={6} width="100%">
          {/** Necessary for form creation */}
          <Input type="hidden" name="form-name" value="contact_form" />
          <Input
            type="hidden"
            name="subject"
            value="New lead from TenKSolutions.com"
          />

          <Stack direction={["column", "row"]} spacing={4} width="100%">
            <FormControl id="name" isRequired>
              <FormLabel textAlign={"center"}>Your Name</FormLabel>
              <InputGroup>
                <InputLeftElement>
                  <BsPerson />
                </InputLeftElement>
                <Input
                  name="name"
                  type="text"
                  autoComplete="name"
                  aria-label="Your Name"
                  variant={"filled"}
                />
              </InputGroup>
            </FormControl>

            <FormControl id="email" isRequired>
              <FormLabel textAlign={"center"}>E-Mail</FormLabel>
              <InputGroup>
                <InputLeftElement>
                  <MdOutlineEmail />
                </InputLeftElement>
                <Input
                  name="email"
                  type="email"
                  autoComplete="email"
                  variant={"filled"}
                  aria-label="Your Best Contact Email"
                />
              </InputGroup>
            </FormControl>
          </Stack>

          <HStack spacing={3} w="100%">
            <FormControl id="companyName" isRequired justifyContent={"stretch"}>
              <FormLabel textAlign={"center"}>Company Name</FormLabel>
              <Input
                name="companyName"
                type="text"
                variant={"filled"}
                aria-label="Your Company Name"
              />
            </FormControl>
            <FormControl id="service">
              <FormLabel textAlign={"center"}>Primary Service Needs</FormLabel>

              <Select variant="filled" size="md">
                <option value="We Need...">Select Your Service</option>
                {FormOptions.services.map((service) => (
                  <option key={service} value={service}>
                    {service}
                  </option>
                ))}
              </Select>
            </FormControl>
          </HStack>

          <FormControl id="message" isRequired>
            <FormLabel textAlign={"center"}>Message</FormLabel>
            <FormHelperText mb={3}>
              Tell us about your needs or specific IT challenges
            </FormHelperText>
            <Textarea
              name="message"
              placeholder="How can we help?"
              autoComplete="off"
              aria-label="Your message"
              noOfLines={6}
              size='md'
            />
          </FormControl>
          <FormControl id="urgency">
            <FormLabel textAlign={"center"}>Urgency Level</FormLabel>

            <RadioGroup size={'md'} name="Urgency">
              <HStack spacing={3} justifyContent={"space-evenly"}>
                {FormOptions.urgencyLevels.map((urgency) => (
                  <Radio
                    key={urgency}
                    value={urgency}
                    colorScheme="colors.brand.primary"
                  >
                    {urgency}
                  </Radio>
                ))}
              </HStack>
            </RadioGroup>
          </FormControl>

          <Button
            type="submit"
            colorScheme="colors.brand.primary"
            width="100%"
            role="button"
            aria-label="Send message"
          >
            Send Message
          </Button>
        </VStack>
      </form>
    </VStack>
  );
};

export default ContactForm;
