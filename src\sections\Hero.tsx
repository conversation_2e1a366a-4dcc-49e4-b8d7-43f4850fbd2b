// import { HeroVideo } from "#components/HeroVideo";
// import LeadGen from "#components/LeadGen";
// import {
//   CheckCircleIcon,
//   InfoOutlineIcon,
//   SearchIcon,
//   StarIcon,
// } from "@chakra-ui/icons";
// import {
//   Box,
//   Container,
//   Flex,
//   Heading,
//   Icon,
//   ScaleFade,
//   Stack,
//   Text,
//   useColorModeValue,
//   useDisclosure,
// } from "@chakra-ui/react";
// import React, { useEffect } from "react";
// import { FaNetworkWired, FaUserShield } from "react-icons/fa/index.js";

// export default function CallToActionWithIllustration() {
//   // const theme = useTheme();
//   const { isOpen, onOpen } = useDisclosure();

//   useEffect(() => {
//     onOpen();
//   }, [onOpen]);

//   // Use Chakra theme animation token for animated gradient
//   // const gradientBg = {
//   //   position: "absolute",
//   //   inset: 0,
//   //   zIndex: 1,
//   //   background:
//   //     "linear-gradient(120deg, rgba(0,120,255,0.18) 0%, rgba(120,0,255,0.18) 100%)",
//   //   backgroundSize: "200% 200%",
//   //   animation: theme.animations?.gradientMove || "none",
//   //   filter: "blur(8px)",
//   //   pointerEvents: "none",
//   // };

//   return (
//     <Container p={0} maxW={"full"} position="relative" zIndex={1}>
//       <ScaleFade initialScale={0.7} in={isOpen}>
//         <HeroVideo
//           videoSrc={"/img/bg/pcback.mp4"}
//           blendMode={useColorModeValue("screen", "multiply")}
//           backgroundColors={useColorModeValue(
//             "colors.brand.secondary.100",
//             "colors.brand.secondary.900"
//           )}
//           // sx={gradientBg}
//         >
//           <Flex
//             direction={{ base: "column", md: "row" }}
//             align={{ base: "stretch", md: "center" }}
//             justify="center"
//             w="full"
//             gap={{ base: 8, md: 12 }}
//           >
//             {/* Glassmorphic hero card */}
//             <Stack
//               flex="1"
//               zIndex={1}
//               spacing={8}
//               // position="relative"
//               w="full"
//               maxW={{ base: "100%", md: "570px" }}
//               // ml={{ lg: 8 }}
//               mt={0}
//               bg={useColorModeValue("whiteAlpha.70", "blackAlpha.60")}
//               borderRadius="2xl"
//               boxShadow="2xl"
//               p={{ base: 4, md: 10 }}
//               backdropFilter="auto"
//               backdropBlur="18px"
//               border="1.5px solid"
//               borderColor={useColorModeValue(
//                 "whiteAlpha.400",
//                 "whiteAlpha.200"
//               )}
//             >
//               <Heading
//                 as="h1"
//                 color={useColorModeValue("gray.900", "gray.50")}
//                 fontWeight="extrabold"
//                 textAlign={{ base: "center", md: "left" }}
//                 letterSpacing="-0.02em"
//                 fontSize={{ base: "2xl", md: "3xl", lg: "4xl" }}
//               >
//                 Unlock Every IT & Connectivity Solution—
//                 <Box as="span" color="primary.500">
//                   One Call. All Options
//                 </Box>
//               </Heading>
//               <Text
//                 fontSize="xl"
//                 color={useColorModeValue("gray.600", "gray.200")}
//                 fontWeight="medium"
//               >
//                 Compare business Internet (DIA, Fiber, Coax), voice (UCaaS),
//                 security & cloud from 200+ top suppliers instantly. All
//                 unbiased, with a real expert—never a chatbot or sales script.
//               </Text>
//               {/* BENEFITS ROW with icons */}
//               <Flex gap={3} wrap="wrap" justify="start">
//                 <Box
//                   px={3}
//                   py={1.5}
//                   bg="whiteAlpha.700"
//                   borderRadius="md"
//                   boxShadow="sm"
//                   display="flex"
//                   alignItems="center"
//                   gap={2}
//                   fontWeight="bold"
//                   fontSize="sm"
//                   color="blue.700"
//                 >
//                   <Icon as={StarIcon} color="yellow.400" /> Instant Quotes
//                 </Box>
//                 <Box
//                   px={3}
//                   py={1.5}
//                   bg="whiteAlpha.700"
//                   borderRadius="md"
//                   boxShadow="sm"
//                   display="flex"
//                   alignItems="center"
//                   gap={2}
//                   fontWeight="bold"
//                   fontSize="sm"
//                   color="purple.700"
//                 >
//                   <Icon as={FaNetworkWired} color="purple.400" /> 200+ Providers
//                 </Box>
//                 <Box
//                   px={3}
//                   py={1.5}
//                   bg="whiteAlpha.700"
//                   borderRadius="md"
//                   boxShadow="sm"
//                   display="flex"
//                   alignItems="center"
//                   gap={2}
//                   fontWeight="bold"
//                   fontSize="sm"
//                   color="green.700"
//                 >
//                   <Icon as={CheckCircleIcon} color="green.400" /> Zero Carrier
//                   Bias
//                 </Box>
//                 <Box
//                   px={3}
//                   py={1.5}
//                   bg="whiteAlpha.700"
//                   borderRadius="md"
//                   boxShadow="sm"
//                   display="flex"
//                   alignItems="center"
//                   gap={2}
//                   fontWeight="bold"
//                   fontSize="sm"
//                   color="red.600"
//                 >
//                   <Icon as={FaUserShield} color="red.400" /> Free Security
//                   Assessments
//                 </Box>
//                 <Box
//                   px={3}
//                   py={1.5}
//                   bg="whiteAlpha.700"
//                   borderRadius="md"
//                   boxShadow="sm"
//                   display="flex"
//                   alignItems="center"
//                   gap={2}
//                   fontWeight="bold"
//                   fontSize="sm"
//                   color="orange.600"
//                 >
//                   <Icon as={SearchIcon} color="orange.400" /> Free Dark Web
//                   Screening
//                 </Box>
//                 <Box
//                   px={3}
//                   py={1.5}
//                   bg="whiteAlpha.700"
//                   borderRadius="md"
//                   boxShadow="sm"
//                   display="flex"
//                   alignItems="center"
//                   gap={2}
//                   fontWeight="bold"
//                   fontSize="sm"
//                   color="yellow.700"
//                 >
//                   <Icon as={InfoOutlineIcon} color="yellow.400" /> Free Network
//                   Assessments
//                 </Box>
//               </Flex>
//               {/* Trust or CTA row */}
//               <Flex
//                 align="center"
//                 gap={4}
//                 justify={{ base: "center", md: "space-between" }}
//               >
//                 <Text fontSize="sm" color="green.700" fontWeight="semibold">
//                   ✓ No obligation
//                 </Text>
//                 <Text fontSize="sm" color="blue.700" fontWeight="semibold">
//                   ✓ Quick response
//                 </Text>
//                 <Text fontSize="sm" color="gray.500">
//                   DMV & Nationwide
//                 </Text>
//               </Flex>
//             </Stack>
//             {/* Glassmorphic LeadGen card */}
//             <Box
//               flex="1"
//               maxW="md"
//               w="full"
//               alignSelf="center"
//               bg={useColorModeValue("whiteAlpha.80", "blackAlpha.700")}
//               borderRadius="2xl"
//               boxShadow="2xl"
//               p={{ base: 4, md: 8 }}
//               backdropFilter="auto"
//               backdropBlur="18px"
//               border="1.5px solid"
//               opacity={0.7}
//               borderColor={useColorModeValue(
//                 "whiteAlpha.400",
//                 "whiteAlpha.200"
//               )}
//               zIndex={2}
//             >
//               <LeadGen />
//             </Box>
//           </Flex>
//           {/* <Box
//             maxW="container.xl"
//             mx="auto"
//             px={{ base: 4, lg: 8 }}
//             position="relative"
//             zIndex={2}
//           >
//             <Heading
//               as="h1"
//               fontWeight={600}
//               fontSize={{ base: "4xl", md: "6xl", lg: "7xl" }}
//               lineHeight="110%"
//               textAlign="center"
//               textShadow="0 2px 4px rgba(0,0,0,0.2)"
//               bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
//               bgClip="text"
//               animation="fadeIn 2s ease-in"
//             >
//               Empower Your Business Growth
//             </Heading>

//             <Text
//               as="h2"
//               mt={6}
//               fontSize={{ base: "xl", md: "2xl", lg: "3xl" }}
//               textAlign="center"
//               maxW="800px"
//               mx="auto"
//               color={useColorModeValue("gray.700", "gray.300")}
//               textShadow="0 1px 2px rgba(0,0,0,0.1)"
//             >
//               Delivering innovative IT solutions that drive success for professional services firms in the DMV-area
//             </Text>
//           </Box> */}
//         </HeroVideo>
//       </ScaleFade>
//     </Container>
//   );
// }
