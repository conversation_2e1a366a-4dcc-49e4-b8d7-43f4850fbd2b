import { Box, Spinner, useColorModeValue } from "@chakra-ui/react";
import { motion } from "framer-motion";
import * as React from 'react';

const MotionBox = motion(Box);

export const LoadingSpinner = () => {
  return (
    <MotionBox
      position="fixed"
      top="50%"
      left="50%"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      style={{ transform: 'translate(-50%, -50%)' }}
    >
      <Spinner
        thickness="4px"
        speed="0.65s"
        emptyColor="gray.200"
        color={useColorModeValue("brand.primary.500", "brand.secondary.500")}
        size="xl"
      />
    </MotionBox>
  );
};