import LeadGen from "#components/LeadGen";
import {
  CheckCircleIcon,
  InfoOutlineIcon,
  SearchIcon,
  StarIcon,
} from "@chakra-ui/icons";
import {
  Box,
  Flex,
  Heading,
  Icon,
  Image,
  Stack,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";
import { FaNetworkWired, FaUserShield } from "react-icons/fa/index.js";

// You may want to create a types file later
// interface Metric {
//   number: string;
//   label: string;
// }

// const metrics: Metric[] = [
//   { number: "200+", label: "Technology Partners" },
//   { number: "15+", label: "Years Experience" },
//   { number: "98%", label: "Client Retention" },
// ];

// const MotionBox = motion(Box);

export const HeroSection: React.FC = () => {
  // const bgGradient = useColorModeValue(
  //   "linear(to-r, blue.600, purple.600)",
  //   "linear(to-r, blue.700, purple.700)"
  // );

  const overlayGradient = useColorModeValue(
    "linear(to-r, colors.brand.primary.200 15%, colors.brand.primary.100 50%, blue.200 100%)",
    "linear(to-r, blue.600 15%, colors.brand.primary.700 50%, colors.brand.primary.600 100%)"
  );
  const blendMode = useColorModeValue("screen", "color-burn");

  return (
    <Box
      as="section"
      position="relative"
      // h={{ base: "100vh", md: "100vh" }}
      minH="700px"
      p={3}
      // color="white"
      // overflow="hidden"
    >
      {/* Video Background */}
      <Box
        as="video"
        autoPlay
        muted
        loop
        playsInline
        position="absolute"
        top="0"
        left="0"
        w="100%"
        h="100%"
        objectFit="cover"
        zIndex={0}
      >
        <source src="/img/bg/pcback.mp4" type="video/mp4" />
      </Box>

      {/* Gradient Overlay */}
      <Box
        position="absolute"
        top="0"
        left="0"
        w="100%"
        h="100%"
        bgGradient={overlayGradient}
        blendMode={blendMode}
        zIndex={1}
      />

      {/* Content */}

      <Flex
        direction={{ base: "column", md: "row" }}
        align={{ base: "stretch", md: "center" }}
        justify="center"
        w="full"
        gap={{ base: 4, md: 12 }} // reduce gap on mobile
        px={{ base: 0, md: 2 }} // add horizontal padding on md
      >
        {/* Glassmorphic hero card */}

        <Stack
          flex="1"
          zIndex={1}
          spacing={{ base: 5, md: 8 }} // tighter spacing on mobile
          w="full"
          maxW={{ base: "100%", md: "570px" }}
          mt={0}
          bg={useColorModeValue("whiteAlpha.70", "blackAlpha.60")}
          borderRadius="2xl"
          boxShadow="2xl"
          p={{ base: 3, md: 10 }} // less padding on mobile
          backdropFilter="auto"
          backdropBlur="18px"
          border="1.5px solid"
          borderColor={useColorModeValue("whiteAlpha.400", "whiteAlpha.200")}
        >
          <Heading
            as="h1"
            color={useColorModeValue("gray.900", "gray.50")}
            fontWeight="extrabold"
            textAlign={{ base: "center", md: "left" }}
            letterSpacing="-0.02em"
            fontSize={{ base: "xl", md: "3xl", lg: "4xl" }} // smaller on mobile
            lineHeight={{ base: 1.2, md: 1.1 }}
          >
            Unlock Every IT & Connectivity Solution—
            <Box as="span" color="primary.500">
              One Call. All Options
            </Box>
          </Heading>
          <Text
            fontSize={{ base: "md", md: "xl" }} // smaller on mobile
            color={useColorModeValue("gray.600", "gray.200")}
            fontWeight="medium"
            textAlign={{ base: "center", md: "left" }}
            lineHeight={{ base: 1.5, md: 1.4 }}
          >
            Compare business Internet (DIA, Fiber, Coax), voice (UCaaS),
            security & cloud from 200+ top suppliers instantly. All unbiased,
            with a real expert—never a chatbot or sales script.
          </Text>
          {/* BENEFITS ROW with icons */}
          <Flex gap={2} wrap="wrap" justify={{ base: "center", md: "start" }}>
            {[
              {
                label: "Instant Quotes",
                icon: StarIcon,
                color: "blue.700",
                iconColor: "yellow.400",
              },
              {
                label: "200+ Providers",
                icon: FaNetworkWired,
                color: "purple.700",
                iconColor: "purple.400",
              },
              {
                label: "Zero Carrier Bias",
                icon: CheckCircleIcon,
                color: "green.700",
                iconColor: "green.400",
              },
              {
                label: "Free Security Assessments",
                icon: FaUserShield,
                color: "red.600",
                iconColor: "red.400",
              },
              {
                label: "Free Dark Web Screening",
                icon: SearchIcon,
                color: "orange.600",
                iconColor: "orange.400",
              },
              {
                label: "Free Network Assessments",
                icon: InfoOutlineIcon,
                color: "yellow.700",
                iconColor: "yellow.400",
              },
            ].map((item) => (
              <BenefitBadge
                key={item.label}
                label={item.label}
                icon={item.icon}
                color={item.color}
                iconColor={item.iconColor}
              />
            ))}
          </Flex>
          {/* Trust or CTA row */}
          <Flex
            align="center"
            gap={{ base: 2, md: 4 }}
            justify={{ base: "center", md: "space-between" }}
            direction={{ base: "column", md: "row" }} // stack on mobile
            mt={{ base: 2, md: 0 }}
          >
            <Text fontSize="sm" color="green.700" fontWeight="semibold">
              ✓ No obligation
            </Text>
            <Text fontSize="sm" color="blue.700" fontWeight="semibold">
              ✓ Quick response
            </Text>
            <Text fontSize="sm" color="red.600" fontWeight="semibold">
              ✓ DMV & Nationwide
            </Text>
          </Flex>
          <Image src="/img/logo-no-background.webp" />
        </Stack>
        {/* Glassmorphic LeadGen card */}
        <Box
          flex="1"
          maxW={{ base: "full", md: "lg" }}
          w="full"
          alignSelf="center"
          bg={useColorModeValue("whiteAlpha.80", "blackAlpha.700")}
          borderRadius="2xl"
          boxShadow="2xl"
          p={{ base: 0, md: 8 }} // less padding on mobile
          backdropFilter="auto"
          backdropBlur="18px"
          border="1.5px solid"
          opacity={0.85}
          borderColor={useColorModeValue("whiteAlpha.400", "whiteAlpha.200")}
          zIndex={2}
          mt={{ base: 6, md: 0 }} // add margin on mobile
        >
          <LeadGen />
        </Box>
      </Flex>
    </Box>
  );
};

const BenefitBadge = ({
  label,
  icon,
  color,
  iconColor,
}: {
  label: string;
  icon: any;
  color: string;
  iconColor: string;
}) => {
  return (
    <Box
      px={3}
      py={1.5}
      bg="whiteAlpha.700"
      borderRadius="md"
      boxShadow="sm"
      display="flex"
      alignItems="center"
      gap={2}
      fontWeight="bold"
      fontSize="sm"
      color={color} //"orange.600"
    >
      <Icon
        as={icon}
        color={iconColor}
        display={{ base: "none", md: "inherit" }}
      />
      {label}
    </Box>
  );
};
