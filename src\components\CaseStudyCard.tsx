"use client";

import { slugify } from "#utils/utils";
import {
  Box,
  Divider,
  Heading,
  HStack,
  Image,
  Link,
  Text,
  useColorModeValue,
  VStack,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";
import { ReactMarkdown } from "react-markdown/lib/react-markdown";

const MotionBox = motion(Box);

interface CaseStudyCardProps {
  title: string;
  imgSrc: string;
  companyDesc: string;
  companyName: string;
  summary: string;
  uri: string;
}

export const CaseStudyCard: React.FC<CaseStudyCardProps> = ({
  title,
  imgSrc,
  companyDesc,
  companyName,
  summary,
  uri,
}) => {
  const bg = useColorModeValue("blackAlpha.100", "whiteAlpha.100");
  const bgLine = useColorModeValue(
    "colors.brand.primary.900",
    "colors.brand.primary.100"
  );

  return (
    <MotionBox
      bg={bg}
      p={6}
      rounded="lg"
      shadow="lg"
      id={slugify(companyName)}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 500 }}
    >
      <VStack spacing={3} align="start">
        <Link isExternal href={uri}>
          <HStack spacing={3}>
            <Box h={10} overflow="hidden" rounded="md">
              <Image
                src={imgSrc}
                fallbackSrc={"/img/clients/default.webp"}
                fallbackStrategy="onError"
                alt={`${companyName} logo`}
                objectFit="contain"
                h="100%"
                w="100%"
              />
            </Box>
            <Heading fontSize="xl" as={"h2"}>
              {companyName}
            </Heading>
          </HStack>
        </Link>
        <Text fontSize="sm" color="gray.500">
          {companyDesc}
        </Text>
        <Divider my={2} bgColor={bgLine} />
        <Heading fontSize="lg" as={"h3"}>
          {title}
        </Heading>
        <ReactMarkdown
          components={{
            li: ({ ...props }) => {
              //removes odd console error
              props["ordered"] = "false" as unknown as boolean;
              return (
                <li
                  {...props}
                  style={{
                    marginLeft: "1rem",
                    paddingLeft: "0.5rem",
                    marginBottom: "0.3rem",
                  }}
                />
              );
            },
          }}
          // transition="all 1s ease"
          // _hover={{
          //   whiteSpace: "normal",
          //   overflow: "visible",
          //   noOfLines: 10,
          //   transition: "all 1s ease",
          // }}
          // overflow="hidden"
          // noOfLines={2}
        >
          {summary}
        </ReactMarkdown>
      </VStack>
    </MotionBox>
  );
};
