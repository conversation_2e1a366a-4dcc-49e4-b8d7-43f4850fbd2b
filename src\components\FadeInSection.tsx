import { Box } from "@chakra-ui/react";
import { motion, useInView } from "framer-motion";
import React, { ReactNode, useRef } from "react";

const MotionBox = motion(Box);

export const FadeInSection = ({
  children,
  delay = 0,
}: {
  children: ReactNode;
  delay: number;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <MotionBox
      ref={ref}
      initial={{ y: 50, opacity: 0 }}
      animate={isInView ? { y: 0, opacity: 1 } : { y: 50, opacity: 0 }}
      transition={{ duration: 0.8, delay }}
    >
      {children}
    </MotionBox>
  );
};
