import Footer from "#components/Footer";
import Head from "#components/Head";
import Navigation from "#components/Navigation";
import ThemeWrapper from "#components/ThemeWrapper";
import React from "react";
import type { PageContext } from "vike/types";
import { PageContextProvider } from "./PageContextProvider";

function PageShell({
  children,
  pageContext,
}: {
  children: React.ReactNode;
  pageContext: PageContext;
}) {
  return (
    <React.StrictMode>
      <PageContextProvider pageContext={pageContext}>
        <ThemeWrapper>
          <Head />
          <Navigation />
          {children}
          <Footer />
        </ThemeWrapper>
      </PageContextProvider>
    </React.StrictMode>
  );
}

export { PageShell };
