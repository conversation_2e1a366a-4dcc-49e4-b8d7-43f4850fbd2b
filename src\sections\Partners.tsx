import { fadeInUp, staggerChildren } from "#utils/animationVariants";
import { Box, Container, Heading, SimpleGrid, Text, useColorModeValue } from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";
import { PartnerCard } from "#components/PartnerCard";
import { PARTNERS } from "#pages/data";

const MotionBox = motion(Box);

const PartnersSection = () => {
  return (
    <MotionBox
      as="section"
      py={16}
      bg={useColorModeValue("gray.50", "gray.900")}
      variants={staggerChildren}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
    >
      <Container maxW="container.xl">
        <MotionBox variants={fadeInUp} textAlign="center" mb={12}>
          <Heading
            fontSize={{ base: "3xl", md: "4xl" }}
            bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
            bgClip="text"
            mb={4}
          >
            Our Technology Partners
          </Heading>
          <Text fontSize="lg" color={useColorModeValue("gray.600", "gray.400")}>
            Working with industry leaders to deliver exceptional solutions
          </Text>
        </MotionBox>

        <MotionBox variants={staggerChildren}>
          <SimpleGrid
            columns={{ base: 2, md: 3, lg: 4 }}
            spacing={{ base: 4, lg: 8 }}
          >
            {PARTNERS.map((partner) => (
              <MotionBox key={partner.name} variants={fadeInUp}>
                <PartnerCard {...partner} />
              </MotionBox>
            ))}
          </SimpleGrid>
        </MotionBox>
      </Container>
    </MotionBox>
  );
};

export default PartnersSection;
