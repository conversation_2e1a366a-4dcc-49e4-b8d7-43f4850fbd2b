"use client";

import {
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Textarea,
  VStack,
} from "@chakra-ui/react";
import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect, useState } from "react";

const MotionFormErrorMessage = motion(FormErrorMessage);

interface FormField {
  name: string;
  label: string;
  type: "text" | "email" | "textarea";
  required?: boolean;
  minLength?: number;
}

interface AccessibleFormProps {
  fields: FormField[];
  onSubmit: (data: Record<string, string>) => void;
  submitText?: string;
}

export const AccessibleForm = ({
  fields,
  onSubmit,
  submitText = "Submit",
}: AccessibleFormProps) => {
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const validateField = (value: string, field: FormField) => {
    if (field.required && !value) {
      return `${field.label} is required`;
    }
    if (field.type === "email" && !/\S+@\S+\.\S+/.test(value)) {
      return "Please enter a valid email address";
    }
    if (field.minLength && value.length < field.minLength) {
      return `${field.label} must be at least ${field.minLength} characters`;
    }
    return "";
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const newErrors: Record<string, string> = {};
    fields.forEach((field) => {
      const error = validateField(formData[field.name] || "", field);
      if (error) {
        newErrors[field.name] = error;
      }
    });

    if (Object.keys(newErrors).length === 0) {
      await onSubmit(formData);
    }

    setErrors(newErrors);
    setIsSubmitting(false);
  };

  if (!mounted) {
    return null; // Prevent hydration issues by not rendering until client-side
  }

  return (
    <form onSubmit={handleSubmit} noValidate>
      <VStack spacing={4} align="stretch">
        {fields.map((field) => (
          <FormControl
            key={field.name}
            isInvalid={!!errors[field.name]}
            isRequired={field.required}
          >
            <FormLabel htmlFor={field.name}>{field.label}</FormLabel>
            {field.type === "textarea" ? (
              <Textarea
                id={field.name}
                name={field.name}
                value={formData[field.name] || ""}
                onChange={(e) => {
                  setFormData({ ...formData, [field.name]: e.target.value });
                  if (errors[field.name]) {
                    setErrors({ ...errors, [field.name]: "" });
                  }
                }}
                onBlur={(e) => {
                  const error = validateField(e.target.value, field);
                  if (error) {
                    setErrors({ ...errors, [field.name]: error });
                  }
                }}
              />
            ) : (
              <Input
                id={field.name}
                name={field.name}
                type={field.type}
                value={formData[field.name] || ""}
                onChange={(e) => {
                  setFormData({ ...formData, [field.name]: e.target.value });
                  if (errors[field.name]) {
                    setErrors({ ...errors, [field.name]: "" });
                  }
                }}
                onBlur={(e) => {
                  const error = validateField(e.target.value, field);
                  if (error) {
                    setErrors({ ...errors, [field.name]: error });
                  }
                }}
              />
            )}
            <AnimatePresence>
              {errors[field.name] && (
                <MotionFormErrorMessage
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  {errors[field.name]}
                </MotionFormErrorMessage>
              )}
            </AnimatePresence>
          </FormControl>
        ))}
        <Button
          type="submit"
          isLoading={isSubmitting}
          loadingText="Submitting"
          bgGradient="linear(to-r, colors.brand.primary.500, colors.brand.secondary.500)"
          color="white"
          _hover={{
            transform: "translateY(-2px)",
            boxShadow: "lg",
          }}
        >
          {submitText}
        </Button>
      </VStack>
    </form>
  );
};
