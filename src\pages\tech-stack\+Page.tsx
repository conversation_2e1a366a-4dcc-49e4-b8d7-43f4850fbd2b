import { themeByA<PERSON> } from "#utils/themes";
import {
  As,
  Box,
  Heading,
  ListIcon,
  ListItem,
  SimpleGrid,
  Text,
  UnorderedList,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import * as React from "react";
import { IconType } from "react-icons";
import { DiResponsive } from "react-icons/di/index.js";
import { FaRegHandshake } from "react-icons/fa/index.js";
import { GiAmericanFootballHelmet } from "react-icons/gi/index.js";
import {
  SiChakraui,
  SiEslint,
  SiExpress,
  SiGit,
  SiGithub,
  SiMarkdown,
  SiNetlify,
  SiNodedotjs,
  SiPrettier,
  SiPwa,
  SiReact,
  SiServerless,
  SiSketch,
  SiTypescript,
  SiVisualstudio,
  SiVite,
  SiVitest,
} from "react-icons/si/index.js";

const heading = "TenK Solutions, LLC Technology Stack";

const frontendTechs = [
  ["React", "For building UIs through components", SiReact],
  ["TypeScript", "This is the way...", SiTypescript],
  ["Vite", "Build tool focused on lightning fast dev server and HMR", SiVite],
  [
    "Chakra UI",
    "Accessible React component library for rapid development",
    SiChakraui,
  ],
  // ["Framer Motion", "Complex animation capabilities for modern UIs", SiFramer],
  // [
  //   "Emotion",
  //   "Enables CSS styling within JavaScript/TypeScript",
  //   SiCsswizardry,
  // ],
  [
    "React Helmet",
    "Handles dynamically updates page metadata and SEO data",
    GiAmericanFootballHelmet,
  ],
  ["React Icons", "Quickly implement vector icons", SiSketch],
  [
    "React Markdown",
    "Display Markdown content through React components",
    SiMarkdown,
  ],
  ["Vecteezy", "Artificial Intelligence Stock Videos", FaRegHandshake],
];

const backendTechs = [
  [
    "Node.js",
    "JavaScript runtime enabling server-side JS execution",
    SiNodedotjs,
  ],
  ["Express", "A flexible Node.js web application framework", SiExpress],
];

const toolingTech = [
  ["Visual Studio Code", "A code editor for web development", SiVisualstudio],
  ["ESLint", "Identifying and reporting on patterns in JavaScript", SiEslint],
  [
    "Prettier",
    "An opinionated code formatter enforcing consistencies",
    SiPrettier,
  ],
  [
    "Vite Plugins",
    "Extend Vite via plugins like vite-plugin-react, vike & vitepress",
    SiVitest,
  ],
];

const infrastructureTech = [
  [
    "Netlify",
    "Provides CDN networking, atomic deployments, & DNS management",
    SiNetlify,
  ],
  ["Github", "Provides code hosting and version control", SiGithub],
];
const methodologiesTech = [
  [
    "Server-side Generation (SSG)",
    "HTML rendered on server for performance, then rendered in browser via javascript",
    SiServerless,
  ],
  ["PWA", "Progressive Web App (PWA) for offline support", SiPwa],
  [
    "Responsive Design",
    "Site adapts smoothly across all device sizes",
    DiResponsive,
  ],
  [
    "Git Workflow",
    "Following proven practices like branching, PRs, semantic versioning",
    SiGit,
  ],
];

const COLORS = themeByAI;

export default function TechPage() {
  const bg_fg_color = useColorModeValue(
    COLORS.colors.brand.primary[300],
    COLORS.colors.brand.primary[50]
  );
  const urlEncodedColor = encodeURIComponent(bg_fg_color);
  const patternOpacity = 0.2;
  const bg_gradient = useColorModeValue(
    "linear(to-br, colors.brand.primary.50 0%, colors.brand.secondary.50 100%)",
    "linear(to-br, colors.brand.primary.800 0%, colors.brand.secondary.800 100%)"
  );
  return (
    <Box
      as="main"
      minH="100vh"
      position="relative"
      overflow="hidden"
      style={{
        padding: "3em",
        backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='199' viewBox='0 0 100 199'%3E%3Cg fill='${urlEncodedColor}' fill-opacity='${patternOpacity}'%3E%3Cpath d='M0 199V0h1v1.99L100 199h-1.12L1 4.22V199H0zM100 2h-.12l-1-2H100v2z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      {/* Hero Section */}
      <Box
        position="relative"
        w="full"
        py={{ base: 10, md: 20 }}
        px={{ base: 2, md: 8 }}
        mb={12}
        zIndex={1}
      >
        {/* Animated SVG/Gradient BG */}

        {/* <Box
          position="absolute"
          top="-10%"
          left="-10%"
          w="120%"
          h="120%"
          zIndex={0}
          bgGradient="radial(ellipse at 60% 40%, colors.brand.primary.200 0%, transparent 70%)"
          opacity={0.18}
          filter="blur(32px)"
        /> */}
        <VStack
          spacing={6}
          align="center"
          position="relative"
          zIndex={1}
          // bg={useColorModeValue("whiteAlpha.80", "blackAlpha.700")}
          bgGradient={bg_gradient}
          borderRadius="2xl"
          boxShadow="2xl"
          p={{ base: 6, md: 14 }}
          backdropFilter="auto"
          backdropBlur="18px"
        >
          <Heading
            as="h1"
            size="2xl"
            textAlign="center"
            fontWeight="extrabold"
            letterSpacing="-0.03em"
            lineHeight={1.1}
          >
            {heading}
          </Heading>
          <Text
            fontSize={{ base: "lg", md: "2xl" }}
            color={useColorModeValue("gray.700", "gray.200")}
            textAlign="center"
            maxW="2xl"
            fontWeight="medium"
          >
            Built for 2026 and beyond: bleeding-edge frameworks, cloud-native
            infrastructure, and a relentless focus on performance,
            accessibility, and security.
          </Text>
        </VStack>
      </Box>
      {/* Tech Sections Grid */}
      <SimpleGrid
        columns={{ base: 1, md: 2 }}
        spacing={10}
        px={{ base: 2, md: 8 }}
      >
        <Section title="Frontend Technologies" techs={frontendTechs} />
        <Section title="Backend Technologies" techs={backendTechs} />
        <Section
          title="Infrastructure Technologies"
          techs={infrastructureTech}
        />
        <Section title="Tooling" techs={toolingTech} />
        <Section title="Methodologies" techs={methodologiesTech} />
      </SimpleGrid>
    </Box>
  );
}

export function Section({
  title,
  techs,
}: {
  title: string;
  techs: (string | IconType)[][];
}) {
  const bg_color = useColorModeValue(
    COLORS.colors.brand.secondary[50],
    COLORS.colors.brand.secondary[900]
  );
  const border_color = useColorModeValue(
    "colors.brand.primary.100",
    "colors.brand.primary.900"
  );
  return (
    <Box
      as={motion.div}
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      // transition={{ duration: 0.7, type: "spring" }}
      viewport={{ once: true }}
      w="full"
      p={{ base: 5, md: 8 }}
      borderRadius="2xl"
      boxShadow="xl"
      bg={bg_color}
      borderWidth={2}
      borderColor={border_color}
      position="relative"
      overflow="hidden"
      _hover={{
        boxShadow: "2xl",
        transform: "translateY(-4px) scale(1.01)",
      }}
    >
      <Heading as="h2" size="lg" mb={4} letterSpacing="-0.01em">
        {title}
      </Heading>
      <Box w="full" px={2}>
        <TechList techs={techs} />
      </Box>
    </Box>
  );
}

export function TechList({ techs }: { techs: (As | string | any)[][] }) {
  return (
    <UnorderedList spacing={3} fontSize={{ base: "md", md: "lg" }}>
      {techs.map((tech, i) => (
        <ListItem
          key={i}
          display="flex"
          alignItems="center"
          gap={3}
          _hover={{
            color: "colors.brand.primary.500",
            fontWeight: "bold",
            bg: "colors.brand.primary.50",
          }}
          borderRadius="md"
          px={2}
          py={1}
          transition="all 0.2s"
        >
          <ListIcon as={tech[2]} boxSize={6} color="colors.brand.primary.400" />
          <Box as="span">
            <strong>{tech[0]}</strong> - {tech[1]}
          </Box>
        </ListItem>
      ))}
    </UnorderedList>
  );
}
