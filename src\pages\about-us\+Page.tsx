import {
  Badge,
  Box,
  Button,
  Container,
  Grid,
  Heading,
  HStack,
  Icon,
  Image,
  Link,
  List,
  ListIcon,
  ListItem,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import React from "react";
import {
  FiArrowRight,
  FiCheck,
  FiCloud,
  FiHardDrive,
  FiHeadphones,
  FiMessageCircle,
  FiPhone,
  FiServer,
  FiShield,
  FiSmartphone,
  FiWifi,
} from "react-icons/fi/index.js";

// IQA links for each solution
const IQA_LINKS: Record<string, string> = {
  "Unified Communications":
    "https://10k.gopathfinder.net/#/iqa-form/ucaas?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  "Contact Center Solutions":
    "https://10k.gopathfinder.net/#/iqa-form/contactcenterasaservice?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  Cybersecurity:
    "https://10k.gopathfinder.net/#/iqa-form/security?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  "Connectivity & SDN":
    "https://10k.gopathfinder.net/#/iqa-form/connectivity?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  "Cloud Compute / IaaS":
    "https://10k.gopathfinder.net/#/iqa-form/infrastructureasaservice?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  "Colocation & Data Center":
    "https://10k.gopathfinder.net/#/iqa-form/colocation?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  "Wireless & IoT":
    "https://10k.gopathfinder.net/#/iqa-form/wirelessiot?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  "CPaaS & Messaging":
    "https://10k.gopathfinder.net/#/iqa-form/emailproductivity?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
  "Backup as a Service / DRaaS":
    "https://10k.gopathfinder.net/#/iqa-form/backupdisasterrecovery?name=Kiel%20Byrne&email=kiel%40tenksolutions.com",
};

// components/AboutPage.tsx
const AboutPage: React.FC = () => {
  const bgGradient = useColorModeValue(
    "linear(to-r, blue.600 15%, purple.500)",
    "linear(to-r, blue.900 15%, purple.800)"
  );

  const bgColor = useColorModeValue("white", "gray.800");
  return (
    <Box as="main">
      {/* Hero Section - Dynamic & Premium */}
      <Box
        position="relative"
        overflow="hidden"
        bg={useColorModeValue("gray.50", "gray.900")}
        minH="90vh"
        display="flex"
        alignItems="center"
      >
        {/* Animated Background Elements */}
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          opacity="0.1"
          zIndex={0}
        >
          <Grid templateColumns="repeat(8, 1fr)" gap={4} p={8}>
            {Array(64)
              .fill(0)
              .map((_, i) => (
                <Box
                  key={i}
                  as={motion.div}
                  animate={{
                    opacity: [0.3, 0.6, 0.3],
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: `all ${Math.random() * 3 + 2}s`,
                    repeat: "all",
                    repeatType: "reverse",
                  }}
                  h="50px"
                  bg="blue.500"
                  borderRadius="md"
                />
              ))}
          </Grid>
        </Box>

        <Container maxW="container.xl" position="relative" zIndex={1}>
          <Stack spacing={20}>
            {/* Hero Content */}
            <Grid
              templateColumns={{ base: "1fr", lg: "1.2fr 0.8fr" }}
              gap={12}
              alignItems="center"
            >
              <Stack spacing={8}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  <Stack spacing={6}>
                    <Text color="blue.500" fontWeight="bold" fontSize="xl">
                      LEADING SYSTEMS INTEGRATION CONSULTANCY
                    </Text>
                    <Heading
                      as="h1"
                      size="3xl"
                      lineHeight="1.2"
                      bgClip="text"
                      bgGradient={bgGradient}
                    >
                      Transforming Businesses Through Technology Excellence
                    </Heading>
                    <Text fontSize="xl" color="gray.600" maxW="xl">
                      15+ years of delivering enterprise-grade IT solutions that
                      drive innovation and scale operations for DMV-area
                      businesses.
                    </Text>
                  </Stack>
                </motion.div>

                <HStack spacing={8} pt={4}>
                  {[
                    { label: "Technology Partners", value: "200+" },
                    { label: "Years Experience", value: "15+" },
                    { label: "Client Retention", value: "98%" },
                  ].map((stat) => (
                    <Stack key={stat.label}>
                      <Text
                        fontSize="3xl"
                        fontWeight="bold"
                        bgGradient={bgGradient}
                        bgClip="text"
                      >
                        {stat.value}
                      </Text>
                      <Text fontSize="sm" color="gray.500">
                        {stat.label}
                      </Text>
                    </Stack>
                  ))}
                </HStack>
              </Stack>

              <Box
                position="relative"
                _after={{
                  content: '""',
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  width: "140%",
                  height: "140%",
                  bg: "blue.500",
                  opacity: 0.1,
                  borderRadius: "full",
                  zIndex: -1,
                }}
              >
                <Image
                  src="/img/features/ft_people.webp"
                  alt="Enterprise Technology"
                  borderRadius="xl"
                  objectFit="cover"
                />
              </Box>
            </Grid>

            {/* Solutions Showcase */}
            <Stack spacing={12}>
              <Stack spacing={4} textAlign="center">
                <Heading size="xl">Enterprise Solutions</Heading>
                <Text fontSize="lg" color="gray.600" maxW="2xl" mx="auto">
                  Comprehensive technology solutions designed for modern
                  enterprises
                </Text>
              </Stack>

              <SimpleGrid
                columns={{ base: 1, md: 2, lg: 3 }}
                spacing={8}
                pt={8}
              >
                {[
                  {
                    title: "Unified Communications",
                    icon: FiPhone,
                    description:
                      "Enterprise-grade UCaaS platforms for modern business communication.",
                    features: [
                      "Seamless global voice and video collaboration",
                      "Integration with leading business apps",
                      "Highly scalable and secure",
                    ],
                    suppliers: ["RingCentral", "8x8", "Vonage Business"],
                  },
                  {
                    title: "Contact Center Solutions",
                    icon: FiHeadphones,
                    description:
                      "Omnichannel CCaaS enabling superior customer experiences.",
                    features: [
                      "Unified voice, chat, and social channels",
                      "Advanced analytics and call routing",
                      "Supports remote and hybrid teams",
                    ],
                    suppliers: ["Five9", "Talkdesk", "Genesys"],
                  },
                  {
                    title: "Cybersecurity",
                    icon: FiShield,
                    description:
                      "Comprehensive security to protect against evolving cyber threats.",
                    features: [
                      "24/7 threat monitoring",
                      "Data loss prevention and zero trust frameworks",
                      "Security compliance audits",
                    ],
                    suppliers: [
                      "Trustwave",
                      "eSentire",
                      "Corvid Cyberdefense, LLC",
                    ],
                  },
                  {
                    title: "Connectivity & SDN",
                    icon: FiWifi,
                    description:
                      "High-speed and reliable internet, MPLS, SD-WAN, and dedicated circuits.",
                    features: [
                      "Nationwide fiber/DIA and wireless options",
                      "Business-class SLAs and guaranteed uptime",
                      "Dynamic bandwidth management",
                    ],
                    suppliers: ["Zayo", "Lumen", "AT&T"],
                  },
                  {
                    title: "Cloud Compute / IaaS",
                    icon: FiCloud,
                    description:
                      "Flexible public, private, and hybrid cloud compute resources.",
                    features: [
                      "On-demand, scalable compute and storage",
                      "Disaster recovery and backup options",
                      "Hybrid cloud deployments",
                    ],
                    suppliers: ["RapidScale", "Rackspace", "TierPoint"],
                  },
                  {
                    title: "Colocation & Data Center",
                    icon: FiServer,
                    description:
                      "Secure, high-performance data center and colocation services.",
                    features: [
                      "Geographically diverse facilities",
                      "Carrier-neutral and redundant connectivity",
                      "Managed power and compliance support",
                    ],
                    suppliers: ["Digital Realty", "Equinix", "CoreSite"],
                  },
                  {
                    title: "Wireless & IoT",
                    icon: FiSmartphone,
                    description:
                      "Mobility, fixed wireless, IoT connectivity and device management.",
                    features: [
                      "Nationwide 5G and LTE coverage",
                      "IoT SIMs, MDM, and enterprise mobility management",
                      "In-building DAS and wireless backup",
                    ],
                    suppliers: [
                      "T-Mobile powered by Hyperion",
                      "Verizon",
                      "Advantix",
                    ],
                  },
                  {
                    title: "CPaaS & Messaging",
                    icon: FiMessageCircle,
                    description:
                      "Platform APIs for voice, SMS, and chat integration.",
                    features: [
                      "Programmable voice and messaging APIs",
                      "Global reach and scalability",
                      "Rich business communications experiences",
                    ],
                    suppliers: ["Sinch", "Twilio", "IntelePeer"],
                  },
                  {
                    title: "Backup as a Service / DRaaS",
                    icon: FiHardDrive,
                    description:
                      "Protect business data with automated, offsite backup and disaster recovery.",
                    features: [
                      "Automated multi-site backups",
                      "Rapid failover and recovery",
                      "Compliance-ready storage solutions",
                    ],
                    suppliers: [
                      "Assured Data Protection",
                      "RapidScale",
                      "Dataprise",
                    ],
                  },
                ].map((solution) => (
                  <motion.div
                    key={solution.title}
                    whileHover={{ y: -8 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Box
                      p={8}
                      bg={bgColor}
                      borderRadius="xl"
                      boxShadow="xl"
                      height="full"
                    >
                      <Stack spacing={6}>
                        <Icon as={solution.icon} boxSize={8} color="blue.500" />
                        <Heading size="md">{solution.title}</Heading>
                        <Text color="gray.600">{solution.description}</Text>
                        <List spacing={3}>
                          {solution.features.map((feature) => (
                            <ListItem
                              key={feature}
                              display="flex"
                              alignItems="center"
                            >
                              <ListIcon as={FiCheck} color="blue.500" />
                              <Text>{feature}</Text>
                            </ListItem>
                          ))}
                        </List>
                        <Stack
                          direction="row"
                          wrap={"wrap"}
                          fontFamily={"mono"}
                        >
                          <Heading size="xs">Suppliers:</Heading>
                          {solution.suppliers.map((supplier) => (
                            <Badge key={supplier}>{supplier}</Badge>
                          ))}
                        </Stack>
                        {IQA_LINKS[solution.title] && (
                          <Button
                            as={Link}
                            href={IQA_LINKS[solution.title]}
                            colorScheme="blue"
                            size="md"
                            mt={2}
                            target="_blank"
                            rel="noopener noreferrer"
                            w="full"
                          >
                            Start Assessment
                          </Button>
                        )}
                      </Stack>
                    </Box>
                  </motion.div>
                ))}
              </SimpleGrid>
            </Stack>

            {/* Premium CTA */}
            <Box
              bg={useColorModeValue("blue.50", "blue.900")}
              borderRadius="2xl"
              p={{ base: 8, md: 12 }}
              position="relative"
              overflow="hidden"
            >
              <Box
                position="absolute"
                top="-20%"
                right="-10%"
                width="400px"
                height="400px"
                bg="blue.500"
                opacity="0.1"
                borderRadius="full"
              />

              <Stack spacing={8} position="relative" maxW="3xl">
                <Stack spacing={4}>
                  <Heading size="lg">
                    Ready to Transform Your Enterprise?
                  </Heading>
                  <Text fontSize="lg" color="gray.600">
                    Schedule a consultation with our experts to discover how we
                    can elevate your technology infrastructure.
                  </Text>
                </Stack>

                <HStack spacing={4}>
                  <Button
                    size="lg"
                    colorScheme="blue"
                    rightIcon={<FiArrowRight />}
                    as={Link}
                    target="_blank"
                    href="https://10k.gopathfinder.net/#/iqa-form/generalinquiry?name=Kiel%20Byrne&email=kiel%40tenksolutions.com"
                  >
                    Get Started
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    as={Link}
                    href="/case-studies"
                  >
                    View Case Studies
                  </Button>
                </HStack>
              </Stack>
            </Box>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
};

export default AboutPage;
