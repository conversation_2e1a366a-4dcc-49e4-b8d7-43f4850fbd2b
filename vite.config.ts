import react from "@vitejs/plugin-react";
import { resolve } from "path";
import vike from "vike/plugin";
import { defineConfig } from "vite";
import eslint from "vite-plugin-eslint";

const resolvePath = (path: string) => resolve(__dirname, path);
const root = resolvePath("src");
const publicDir = resolvePath("src/public");
const outDir = resolvePath("dist");

// https://vitejs.dev/config/
export default defineConfig({
  root,
  plugins: [react(), vike({ prerender: true }), eslint()],
  publicDir,
  build: {
    outDir,
    emptyOutDir: true,
    sourcemap: true,
  },
  resolve: {
    alias: {
      "#components": resolvePath("src/components"),
      "#sections": resolvePath("src/sections"),
      "#pages": resolvePath("src/pages"),
      "#utils": resolvePath("src/utils"),
      "#renderer": resolvePath("src/renderer"),
      "#theme": resolvePath("src/theme"),
    },
  },
});
