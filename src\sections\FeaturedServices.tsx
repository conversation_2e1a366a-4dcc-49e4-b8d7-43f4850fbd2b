// components/FeaturedServices.tsx
import {
  Box,
  Button,
  Container,
  Heading,
  Icon,
  SimpleGrid,
  Stack,
  Text,
  useColorModeValue
} from '@chakra-ui/react';
import React from 'react';
import { FiArrowRight, FiDatabase, FiGrid, FiShield } from 'react-icons/fi/index'; // Install with: npm i react-icons
  
  interface IServiceCard {
    title: string;
    description: string;
    icon: React.ElementType;
    href: string;
  }
  
  const services: IServiceCard[] = [
    {
      title: 'AI & Data Centers',
      description: 'Scale your operations with advanced AI solutions and robust data center infrastructure.',
      icon: FiDatabase,
      href: '/services/ai',
    },
    {
      title: 'Enterprise Security',
      description: 'Comprehensive cybersecurity solutions for multi-location businesses.',
      icon: FiShield,
      href: '/services/security',
    },
    {
      title: 'Microsoft 365',
      description: 'Licensed solutions optimized for organizations with 100+ employees.',
      icon: FiGrid,
      href: '/services/microsoft',
    },
  ];
  
  const ServiceCard: React.FC<IServiceCard> = ({ title, description, icon, href }) => {
    const cardBg = useColorModeValue('white', 'gray.800');
    const cardHoverBg = useColorModeValue('gray.50', 'gray.700');
  
    return (
      <Stack
        as="a"
        href={href}
        spacing={4}
        p={8}
        bg={cardBg}
        borderRadius="xl"
        boxShadow="lg"
        transition="all 0.3s"
        _hover={{
          transform: 'translateY(-8px)',
          bg: cardHoverBg,
          boxShadow: 'xl',
        }}
      >
        <Box
          p={3}
          bg="blue.500"
          color="white"
          borderRadius="lg"
          width="fit-content"
        >
          <Icon as={icon} boxSize={6} />
        </Box>
        
        <Stack spacing={2}>
          <Heading size="md">{title}</Heading>
          <Text color={useColorModeValue('gray.600', 'gray.300')}>
            {description}
          </Text>
        </Stack>
  
        <Button
          variant="link"
          colorScheme="blue"
          rightIcon={<Icon as={FiArrowRight} />}
          alignSelf="flex-start"
          mt={4}
        >
          Learn More
        </Button>
      </Stack>
    );
  };
  
  export const FeaturedServices: React.FC = () => {
    return (
      <Box
        as="section"
        py={{ base: 16, md: 24 }}
        bg={useColorModeValue('gray.50', 'gray.900')}
      >
        <Container maxW="container.xl">
          <Stack spacing={12}>
            <Stack spacing={4} textAlign="center">
              <Heading
                size="2xl"
                fontWeight="bold"
                color={useColorModeValue('gray.900', 'white')}
              >
                Enterprise Solutions
              </Heading>
              <Text
                fontSize="xl"
                color={useColorModeValue('gray.600', 'gray.400')}
                maxW="3xl"
                mx="auto"
              >
                Comprehensive technology solutions designed for growing organizations
              </Text>
            </Stack>
  
            <SimpleGrid
              columns={{ base: 1, md: 2, lg: 3 }}
              spacing={8}
              px={{ base: 4, md: 8 }}
            >
              {services.map((service, index) => (
                <ServiceCard key={index} {...service} />
              ))}
            </SimpleGrid>
  
            <Box textAlign="center">
              <Button
                size="lg"
                colorScheme="blue"
                rightIcon={<Icon as={FiArrowRight} />}
                as="a"
                href="/services"
              >
                View All Services
              </Button>
            </Box>
          </Stack>
        </Container>
      </Box>
    );
  };
  