'use client'

import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormLabel,
  Heading,
  Image,
  Input,
  Select,
  Textarea,
} from "@chakra-ui/react";
import console from "console";
import React, { useState } from "react";
import { z } from "zod";

// Define Zod schema for form validation
const schema = z.object({
  companyName: z.string().min(3),
  industry: z.string(),
  numberOfEmployees: z.number(),
  locations: z.string(),
  painPoints: z.array(z.string()),
  solutionInterests: z.array(z.string()),
  additionalDetails: z.string(),
  name: z.string().min(3),
  email: z.string().email(),
  phone: z.string(),
});
type Schema = z.infer<typeof schema>
const AssessmentFormPage = () => {
  const [formData, setFormData] = useState({
    companyName: "",
    industry: "",
    numberOfEmployees: 0,
    locations: "",
    painPoints: [],
    solutionInterests: [],
    additionalDetails: "",
    name: "",
    email: "",
    phone: "",
  } as Schema);

  const handleChange = (e: any) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]:
        type === "checkbox"
          ? checked
            ? [...(prevData as any)[name], value]
            : (prevData as any)[name].filter((item: any) => item !== value)
          : value,
    }));
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    try {
      schema.parse(formData);
      // Handle form submission
      console.log("Form submitted successfully:", formData);
    } catch (error: any) {
      console.error("Validation error:", error.errors);
    }
  };

  return (
    <Box maxW="800px" mx="auto" p="4">
      {/* TenK's logo and title */}
      <Heading as="h1" mb="4" textAlign="center">
        <Image src="/img/favicon.webp" alt="TenK Solutions Logo" />
        TenK Solutions - Free Technology Consultation
      </Heading>

      {/* Section 1: Business Overview */}
      <Heading as="h2" size="lg" mb="2">
        Business Overview
      </Heading>
      <form onSubmit={handleSubmit}>
        <FormControl id="companyName" mb="4">
          <FormLabel>Company Name</FormLabel>
          <Input
            type="text"
            name="companyName"
            value={formData.companyName}
            onChange={handleChange}
          />
        </FormControl>
        <FormControl id="industry" mb="4">
          <FormLabel>Industry</FormLabel>
          <Select
            name="industry"
            value={formData.industry}
            onChange={handleChange}
          >
            <option value="Technology">Technology</option>
            <option value="Finance">Finance</option>
            {/* Add more industry options as needed */}
          </Select>
        </FormControl>
        <FormControl id="numberOfEmployees" mb="4">
          <FormLabel>Number of Employees</FormLabel>
          <Input
            type="number"
            name="numberOfEmployees"
            value={formData.numberOfEmployees}
            onChange={handleChange}
          />
        </FormControl>
        <FormControl id="locations" mb="4">
          <FormLabel>Location(s)</FormLabel>
          <Input
            type="text"
            name="locations"
            value={formData.locations}
            onChange={handleChange}
          />
        </FormControl>
        <Divider mb="4" />

        {/* Section 2: Technology Pain Points */}
        <Heading as="h2" size="lg" mb="2">
          Technology Pain Points
        </Heading>
        <Checkbox
          name="painPoints"
          value="Outdated/Unreliable Phone System"
          onChange={handleChange}
        >
          Outdated/Unreliable Phone System
        </Checkbox>
        {/* Add more checkboxes for other pain points */}
        <Divider mb="4" />

        {/* Section 3: Solution Interests */}
        <Heading as="h2" size="lg" mb="2">
          Solution Interests
        </Heading>
        <Checkbox
          name="solutionInterests"
          value="Unified Communications (UCaaS)"
          onChange={handleChange}
        >
          Unified Communications (UCaaS)
        </Checkbox>
        {/* Add more checkboxes for other solution interests */}
        <Divider mb="4" />

        {/* Section 4: Additional Details */}
        <Heading as="h2" size="lg" mb="2">
          Additional Details
        </Heading>
        <Textarea
          name="additionalDetails"
          value={formData.additionalDetails}
          onChange={handleChange}
          size="lg"
        />
        <Divider mb="4" />

        {/* Section 5: Contact Information */}
        <Heading as="h2" size="lg" mb="2">
          Contact Information
        </Heading>
        <FormControl id="name" mb="4">
          <FormLabel>Name</FormLabel>
          <Input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
          />
        </FormControl>
        <FormControl id="email" mb="4">
          <FormLabel>Email</FormLabel>
          <Input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
          />
        </FormControl>
        <FormControl id="phone" mb="4">
          <FormLabel>Phone</FormLabel>
          <Input
            type="text"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
          />
        </FormControl>

        {/* Submit button */}
        <Button colorScheme="blue" type="submit" mt="4">
          Submit
        </Button>
      </form>
    </Box>
  );
};

export default AssessmentFormPage;
