import { AspectRatio, Card, Heading, ScaleFade, Stack, Text, useColorModeValue } from "@chakra-ui/react";
import React from "react";

const ServiceArea = () => {
  const blendMode = useColorModeValue('soft-light','color-burn')
  return (
    <ScaleFade initialScale={0.5} in={true}>

      <Stack
      id="service_area"
        overflow={"clip"}
        direction={{ base: "column", sm: "row" }}
        bgColor={"gray.300"}
        p={5}
        spacing={3}
        maxHeight={{ md: "sm", lg: "sm" }}
        bgSize={"cover"}
        bgPosition={"center"}
        bgBlendMode={blendMode}
        bgImage={"/img/bg/dmvsky4.png"}
      >
        <Card width={{ base: "full", sm: "lg", lg: "2xl" }} padding={5} bgColor={useColorModeValue("whiteAlpha.600", "blackAlpha.700")}>
          <Heading as="h2" textAlign={"center"}>
            Service Area
          </Heading>
          <Text
            fontSize={"sm"}
            fontWeight={"semibold"}
            p={3}
            textAlign={"justify"}
            lineHeight={{ md: "7" }}
          >
            Based in Montgomery County, MD, TenK Solutions proudly serves small
            and medium businesses throughout the Greater Washington, DC area. If
            your growing medical practice, professional services firm, private
            school, or other SMB is located within 25 miles of the downtown
            Silver Spring area, TenK Solutions can efficiently become your
            tailored technology partner.
          </Text>
        </Card>
        <AspectRatio width={"full"} opacity={".9"}
        >
          <iframe
            title="service-area"
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            style={{ border: 0, borderRadius: ".3em" }}
            src={`https://snazzymaps.com/embed/562898`}
          />
        </AspectRatio>
      </Stack>
    </ScaleFade>
  );
};

export default ServiceArea;
