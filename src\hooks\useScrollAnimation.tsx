import { useScroll, useTransform } from "framer-motion";
import { useRef } from "react";

interface ScrollAnimationConfig {
  offsetStart?: string;
  offsetEnd?: string;
  inputRange?: number[];
  outputRange?: number[];
}

export const useScrollAnimation = ({
  inputRange = [0, 1],
  outputRange = [0, 1]
}: ScrollAnimationConfig = {}) => {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
  });

  const transform = useTransform(scrollYProgress, inputRange, outputRange);

  return { ref, transform, progress: scrollYProgress };
};