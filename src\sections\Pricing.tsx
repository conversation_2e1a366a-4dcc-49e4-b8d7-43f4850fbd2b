import { PriceCard } from "#components/PriceCard";
import { Box, Heading, Stack, Text, VStack } from "@chakra-ui/react";
import React from "react";
import { BsBroadcast, BsHddStack } from "react-icons/bs/index.js";
import {
  FaBook,
  FaChalkboardTeacher,
  FaCode,
  FaDatabase,
  FaDesktop,
  FaHammer,
  FaHardHat,
  FaLaptop,
  FaNetworkWired,
  FaRocket,
  FaStore,
  FaUserNinja,
  FaUsers,
  FaWifi,
} from "react-icons/fa/index.js";
import {
  GiRadioTower,
  GiTeamIdea,
  GiVideoConference,
} from "react-icons/gi/index.js";
import { LuBrainCircuit } from "react-icons/lu/index.js";
import {
  MdHealthAndSafety,
  MdOutlineRecordVoiceOver,
  MdSecurity,
} from "react-icons/md/index.js";
import { Tb<PERSON><PERSON>, TbSettingsAutomation } from "react-icons/tb/index.js";
const servicesPricing = [
  {
    title: "Managed Services & IT Infrastructure",
    price: 690, //115 * 6 hours / mo
    period: "/month",

    // description:
    //   "White Glove IT Service in the Metro DMV Area",
    items: [
      {
        icon: FaLaptop,
        item: "PC, Mac, Mobile & Software Support",
      },
      {
        icon: LuBrainCircuit,
        item: "AI Implementation",
      },
      {
        icon: TbSettingsAutomation,
        item: "Automation",
      },
      {
        icon: FaNetworkWired,
        item: "Network & Server Administration",
      },
      {
        icon: FaUserNinja,
        item: "Hardware Troubleshooting & Escalation",
      },
      {
        icon: FaChalkboardTeacher,
        item: "Software Training",
      },
      {
        icon: FaBook,
        item: "Technical Documentation",
      },
    ],
    cta: "Retain Services",
    isFeatured: false,
  },
  {
    title: "Systems Integration & Consultation",
    price: 115,
    period: "/hour",
    description: "",
    isFeatured: true,
    items: [
      { item: "New Construction & Upgrades", icon: FaHammer },
      { item: "Wi-Fi System (WLAN)", icon: FaWifi },
      { item: "Colocation", icon: BsHddStack },
      {
        item: "Unified Communications (UCaas)",
        icon: MdOutlineRecordVoiceOver,
      },
      { item: "Cloud Disaster Recovery", icon: MdHealthAndSafety },
      { item: "Cybersecurity", icon: MdSecurity },
      { item: "Collaboration Tools", icon: GiTeamIdea },
      { item: "IoT", icon: BsBroadcast },
      { item: "Conference Center (Cloud)", icon: GiVideoConference },
      { item: "Virtual Desktops (DaaS)", icon: FaDesktop },
      { item: "WAN Technology", icon: GiRadioTower },
    ],
    cta: "Book Now!",
  },
  {
    title: "Website Design & Administration",
    price: 900,
    period: "",
    description: "",
    items: [
      {
        icon: FaCode,
        item: "Manage/Update Website",
      },
      { icon: FaHardHat, item: "Redesign" },
      { icon: FaRocket, item: "New Website" },
      {
        icon: FaUsers,
        item: "Social Media Management",
      },
      {
        icon: FaStore,
        item: "E-commerce",
      },
      {
        icon: FaDatabase,
        item: "Database implementation",
      },
      {
        icon: TbApi,
        item: "API or Authorization implementation",
      },
    ],
    cta: "Let's Build!",
    isFeatured: false,
  },
];
const ThreeTierPricing = () => {
  return (
    <Box p={7} id="Services">
      <VStack spacing={2} textAlign="center" pb={6}>
        <Heading as="h1" fontSize="4xl" my={3}>
          Tailored Pricing for Your Needs
        </Heading>
        <Text fontSize="xl" maxW="container.md">
          <b>Managed IT Services:</b> Monthly retainer plans for small
          businesses.
          <br />
          <b>Systems Integration:</b> Free consultation, choose from 200+
          preferred vendors. <br />
          <b>Custom Software/Web Development:</b> Project-based pricing for
          enterprise clients.
        </Text>
      </VStack>
      <Stack
        direction={{ base: "column", md: "row" }}
        spacing={{ base: 4 }}
        py={5}
        px={{ base: 2 }}
        justify={"center"}
      >
        {servicesPricing.map(
          ({ items, title, price, cta, isFeatured, period }, i) => (
            <PriceCard
              key={i}
              {...{ isFeatured, title, price, period, items, cta }}
            />
          )
        )}
      </Stack>
    </Box>
  );
};
export default ThreeTierPricing;
