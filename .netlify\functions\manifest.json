{"functions": [{"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "C:\\Users\\<USER>\\SITES\\tenKSolutions\\src\\netlify\\functions\\submit-to-zoho.mjs", "name": "submit-to-zoho", "priority": 10, "runtimeVersion": "nodejs22.x", "path": "C:\\Users\\<USER>\\SITES\\tenKSolutions\\.netlify\\functions\\submit-to-zoho.zip", "runtime": "js"}], "system": {"arch": "x64", "platform": "win32"}, "timestamp": 1754284464124, "version": 1}